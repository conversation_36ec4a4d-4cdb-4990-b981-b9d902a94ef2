                        -HC:\src\flutter\packages\flutter_tools\gradle\src\main\groovy
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973
-DCMAKE_ANDROID_NDK=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973
-DC<PERSON>KE_TOOLCHAIN_FILE=C:\Users\<USER>\AppData\Local\Android\Sdk\ndk\27.0.12077973\build\cmake\android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=C:\Users\<USER>\AppData\Local\Android\Sdk\cmake\3.22.1\bin\ninja.exe
-DCMAKE_LIBRARY_OUTPUT_DIRECTORY=C:\flutter app\marafik_app\build\app\intermediates\cxx\RelWithDebInfo\1l61n194\obj\x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=C:\flutter app\marafik_app\build\app\intermediates\cxx\RelWithDebInfo\1l61n194\obj\x86
-DCMAKE_BUILD_TYPE=RelWithDebInfo
-BC:\flutter app\marafik_app\android\app\.cxx\RelWithDebInfo\1l61n194\x86
-GNinja
-Wno-dev
--no-warn-unused-cli
                        Build command args: []
                        Version: 2