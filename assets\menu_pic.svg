<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="445.453" height="245.3" viewBox="0 0 445.453 245.3">
  <defs>
    <linearGradient id="linear-gradient" x1="0.137" y1="0.829" x2="1.116" y2="0.043" gradientUnits="objectBoundingBox">
      <stop offset="0" stop-color="#ffc054"/>
      <stop offset="1" stop-color="#ff416c"/>
    </linearGradient>
    <filter id="Tracé_1">
      <feOffset dy="-4" input="SourceAlpha"/>
      <feGaussianBlur stdDeviation="7" result="blur"/>
      <feFlood flood-opacity="0.169" result="color"/>
      <feComposite operator="out" in="SourceGraphic" in2="blur"/>
      <feComposite operator="in" in="color"/>
      <feComposite operator="in" in2="SourceGraphic"/>
    </filter>
  </defs>
  <g data-type="innerShadowGroup">
    <path id="Tracé_1-2" data-name="Tracé 1" d="M335.777-146.113C293.74-97.8,254.139-53.814,199.191-18.428c-62.942,40.533-74.45,148.734,18.1,201.572,52.2,29.8,60.952,67.546,84.465,102.2,7.495-.524,63.874-4.482,63.874-4.482Z" transform="translate(135.3 374.936) rotate(-86)" fill="url(#linear-gradient)"/>
    <g transform="matrix(1, 0, 0, 1, 0, 0)" filter="url(#Tracé_1)">
      <path id="Tracé_1-3" data-name="Tracé 1" d="M335.777-146.113C293.74-97.8,254.139-53.814,199.191-18.428c-62.942,40.533-74.45,148.734,18.1,201.572,52.2,29.8,60.952,67.546,84.465,102.2,7.495-.524,63.874-4.482,63.874-4.482Z" transform="translate(135.3 374.94) rotate(-86)" fill="#fff"/>
    </g>
  </g>
</svg>