import 'dart:io';
import 'package:location/location.dart';
import 'package:marafik_app/database/complaint.dart';
import 'package:shared_preferences/shared_preferences.dart';

class DataManager {
  static List<File> imageList = [];
  static File? videoFile;
  static File? audioFile;
  static String appName = "MARAFIK BERKANE";
  static LocationData location = resetLocation();
  static String qrCode = "";
  static String myMac = "";
  static bool isArabic = true;
  static SharedPreferences? preferences;

  //
  static int idSector = -1;
  static Function? myNextAction;
  static String footerText = "";
  static int numBus = -1;
  static String nomSector = "";
  static bool canGo = false;
  static List<Complaint> listRequest = [];
  static String SelectedBusNum = "";
  //
  static double curentProgress = 0;
  // static List<dynamic>  newsData=[];

  static Object? previouspage;

  static List image = [];

  static Function? progressSetState;

  static void updateProgress(double prog) {
    curentProgress = prog;
    if (progressSetState != null) {
      progressSetState!();
    }
  }

  static void clear() {
    imageList = [];
    videoFile = null;
    audioFile = null;
    location = resetLocation();
  }

  static List<File> getAttachments() {
    List<File> attachments = [];
    if (audioFile != null) {
      attachments.add(audioFile!);
      attachments.addAll(imageList);
        } else if (videoFile != null) {
      attachments.add(videoFile!);
    }
    return attachments;
  }

  static resetLocation() {
    return LocationData.fromMap({
      "latitude": null,
      "longitude": null,
      "accuracy": null,
      "altitude": null,
      "speed": null,
      "speed_accuracy": null,
      "heading": null,
      "time": null,
      "isMock": 0,
      "verticalAccuracy": null,
      "headingAccuracy": null,
      "elapsedRealtimeNanos": null,
      "elapsedRealtimeUncertaintyNanos": null,
      "satelliteNumber": null,
      "provider": null,
    });
  }
}
