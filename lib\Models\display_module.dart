import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:marafik_app/Models/data_manager.dart';
import 'package:marafik_app/Models/lang.dart';
import 'package:marafik_app/Models/progress_bar.dart';
import 'package:marafik_app/Styles/app_style.dart';

class DisplayModule extends StatefulWidget {
  const DisplayModule(this.content, {this.myBoolbool = false, Key? key})
    : super(key: key);
  final bool myBoolbool;
  final content;
  @override
  _DisplayModuleState createState() => _DisplayModuleState();
}

class _DisplayModuleState extends State<DisplayModule> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    DataManager.canGo = false;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppStayles.appbar(context),
      body:
          widget.myBoolbool
              ? Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  ProgressBar(),
                  Expanded(
                    child: Container(
                      // color: Colors.red,
                      // height: 607.h,
                      width: MediaQuery.of(context).size.width,
                      child: widget.content,
                    ),
                  ),
                  Container(
                    width: MediaQuery.of(context).size.width,
                    height: 75.h,
                    color: AppStayles.fluttertoastBackgroundHexa,
                    child: Center(
                      child: Text(
                        DataManager.footerText,
                        style: AppStayles.textStyles(
                          Colors.white,
                          18.sp,
                          false,
                        ),
                      ),
                    ),
                  ),
                ],
              )
              : Stack(
                alignment: Alignment.center,
                children: [
                  Positioned(top: 0, right: 0, child: ProgressBar()),
                  Positioned(
                    top: 20.h,
                    child: Container(
                      // color: Colors.red,
                      height: widget.myBoolbool ? 530.h : 445.h,
                      width: MediaQuery.of(context).size.width,
                      child: widget.content,
                    ),
                  ),
                  AnimatedPositioned(
                    duration: Duration(milliseconds: 300),
                    bottom: DataManager.canGo == true ? 90.h : 0,
                    child: Center(
                      child: InkWell(
                        onTap: () {
                          DataManager.myNextAction!();
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(5.w),
                            // color: HexColor("#7C3C21"),
                            color: HexColor("#f3a683"),
                          ),
                          width: 90.w,
                          height: 35.w,
                          child: Center(
                            child: Text(
                              ModelLang.next(),
                              style: AppStayles.textStyles(
                                Colors.white,
                                18.sp,
                                true,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                  Positioned(
                    bottom: 0,
                    child: Container(
                      width: MediaQuery.of(context).size.width,
                      height: 75.h,
                      color: AppStayles.fluttertoastBackgroundHexa,
                      child: Center(
                        child: Text(
                          DataManager.footerText,
                          style: AppStayles.textStyles(
                            Colors.white,
                            18.sp,
                            false,
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
    );
  }
}

BoxDecoration boxDecorationImage(String filePath) {
  return BoxDecoration(
    image: DecorationImage(image: AssetImage(filePath), fit: BoxFit.cover),
  );
}
