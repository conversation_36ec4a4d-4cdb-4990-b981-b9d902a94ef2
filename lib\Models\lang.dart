import 'package:marafik_app/Models/data_manager.dart';

class ModelLang {
  static String start() =>
      DataManager.isArabic ? "إسحب للبدء" : "Glisser pour commencer";
  static String firstText() =>
      DataManager.isArabic ? "إضافة عطل" : "AJOUTEZ UNE PANNE";
  static String addImg() =>
      DataManager.isArabic ? "إضافة صورة" : "AJOUTEZ UNE PHOTO";
  static String reportProblem() =>
      DataManager.isArabic ? "الإبلاغ عن مشكلة" : "Reclamer une panne";
  static String report() => DataManager.isArabic ? "الإبلاغ" : "Reclamer";
  static String cancel() => DataManager.isArabic ? "إغلاق" : "Fermer";
  static String eauAssain() =>
      DataManager.isArabic ? "الماء والتطهير" : "Eau & Assainissement";

  static String voirie() => DataManager.isArabic ? "الطرقات" : "Voirie";

  static String securite() => DataManager.isArabic ? "الأمن" : "Sécurité";
  static String voirieMessage() =>
      DataManager.isArabic ? "الطريق أو الأرصفة؟" : "Route ou trottoirs ?";

  static String eauAssainMessage() =>
      DataManager.isArabic ? "ماء أو تطهير؟" : "Eau ou assainissement ?";

  static String securiteMessage() =>
      DataManager.isArabic
          ? "مشكلة أمنية أو سلوك مشبوه؟"
          : "Problème de sécurité ou comportement suspect ?";

  static String home() => DataManager.isArabic ? "الرئيسية" : "Principale";
  static String news() => DataManager.isArabic ? "مستجدات" : "Actualité";

  static String qrScan() =>
      DataManager.isArabic ? "إلتقط الكود" : "Scanner le code QR";
  static String click() => DataManager.isArabic ? "إضغط" : "Cliquez ";
  static String clickGps() =>
      DataManager.isArabic
          ? "إضغط لتحديد موقعك"
          : "Cliquez pour prendre votre localisation";

  static String pickImgOrVid() =>
      DataManager.isArabic ? " فيديو او صورة" : "Vidéo ou photo";

  static String netoyment() => DataManager.isArabic ? "النظافة" : "Nettoiement";
  static String addAllimg() =>
      DataManager.isArabic ? " تم بنجاح !" : "ÉTAPE RÉUSSIE !";
  static String img() => DataManager.isArabic ? " صورة" : "IMAGE ";
  static String imgPack() => DataManager.isArabic ? " صورة" : "Image ";
  static String rappelImg() =>
      DataManager.isArabic
          ? "يجب عليك إلتقاط صورة على الأقل !!!"
          : "Vous devez prendre au moins une photo  !!! ";
  static String video() => DataManager.isArabic ? "فيديو" : "VIDEO";
  static String watchVideo() =>
      DataManager.isArabic ? "شاهد الفيديو" : "Voir la vidéo";
  static String inRecorde() =>
      DataManager.isArabic ? "...جاري التسجيل " : "ENREGISTREMENT EN COURS...";
  static String stopRecorde() =>
      DataManager.isArabic ? "!تم التسجيل" : "ENREGISTREMENT TERMINÉ!";
  static String noAudio() =>
      DataManager.isArabic ? "إبدأ التسجيل" : "Démarrer l'enregistrement";
  static String audio() =>
      DataManager.isArabic ? "تسجيل مقطع صوتي" : "Enregistrement d'un audio";

  static String rappelAudio() =>
      DataManager.isArabic
          ? "المرجو تسجيل مقطع صوتي !!!"
          : "Veuillez prendre un audio !!!";
  static String listType() => DataManager.isArabic ? "libelleAr" : "libelle";
  static String types() =>
      DataManager.isArabic ? " نوع عطل؟" : "TYPE DE PANNE?";
  static String rappelType() =>
      DataManager.isArabic
          ? "الرجاء تحديد نوع عطل !!"
          : "VEUILLEZ SÉLECTIONNER UN TYPE DE PANNE !!";
  static String description() => DataManager.isArabic ? "الوصف" : "DESCRIPTION";
  static String descriptionHint() =>
      DataManager.isArabic
          ? "... اكتب وصفك هنا"
          : "Tappez votre description içi ...";
  static String qrState() => DataManager.isArabic ? "خطأ" : "Erreur";
  static String qrFalse() =>
      DataManager.isArabic ? "هذا الرمز غير موجود" : "Ce code n'existe pas";

  static String erreurEnrg() =>
      DataManager.isArabic
          ? "خطأ في التسجيل !!!"
          : "erreur d 'enregistrement !!!";
  static String succEnrg() =>
      DataManager.isArabic
          ? "!! تم التسجيل عطل بنجاح "
          : "La panne a été enregistrée avec succès !!!";
  static String next() => DataManager.isArabic ? "التالي" : "Suivant";
  static String send() => DataManager.isArabic ? "إرسال" : "ENVOYER";
  static String rappelGps() =>
      DataManager.isArabic
          ? "يجب عليك تفعيل نظام تحديد المواقع الخاص بك"
          : "Vous devez activer votre localisation !!";
  static String alert() => DataManager.isArabic ? "تنبيه" : "Alert";
  static String rappelAutorisations() =>
      DataManager.isArabic
          ? "المرجو الموافقة على الشروط التالية"
          : "Veuillez accepter les autorisations suivantes";
  static String ok() => DataManager.isArabic ? "حسنا" : "Ok";
  static String waitAlert() =>
      DataManager.isArabic ? "الرجاء الانتظار" : "Veuillez patienter...";
  static String connectionState() =>
      DataManager.isArabic ? "معلومات الاتصال" : "Information de Connexion";
  static String noCnx() =>
      DataManager.isArabic ? "!!! غير متصل بالإنترنت" : "Hors ligne !!!";
  static String bac() => DataManager.isArabic ? "الحاويات" : "Bac";

  static String wmsMessage() =>
      DataManager.isArabic
          ? "النفايات أو الحاويات ؟"
          : "Nettoiemment ou Bacs ?";
  static String pcpMessage() =>
      DataManager.isArabic
          ? " بائع او سلعته ؟"
          : "Marchand ou sa marchandise ?";
  static String escapevMessage() =>
      DataManager.isArabic ? "حدائق ؟" : "Jardins ?";
  static String busMessage() =>
      DataManager.isArabic ? "محطة الحافلة او حافلة ؟" : "Gare de bus ou bus ?";
  static String batoireMessage() =>
      DataManager.isArabic ? " اللحم او التاجر ؟" : "Viande ou le marchand ?";
  static String pcp() => DataManager.isArabic ? "أسواق القرب" : "PCP";
  static String batoire() => DataManager.isArabic ? "المجزرة" : "ABATTOIR";
  static String espace() =>
      DataManager.isArabic ? "المساحات الخضراء" : "ESPACE VERT";
  static String wms() => DataManager.isArabic ? "قمامة" : "CDM";
  static String transport() => DataManager.isArabic ? "المواصلات" : "TRANSPORT";
  static String busChoix() =>
      DataManager.isArabic ? "الحافلة أو المحطة" : "Bus ou station";
  static String noRaports() =>
      DataManager.isArabic ? "البلاغات 0" : "Rapports 0";
  static String reclamation() => DataManager.isArabic ? "شكوى" : "Réclamation ";

  static String allMessage() =>
      DataManager.isArabic
          ? "بإستخدامك هذا التطبيق ، يمكنك الإبلاغ عن اي مشكلة  في اقليم بركان"
          : "En utilisant cette application, vous pouvez signaler tout problème dans la province de Berkane";
  static String station() => DataManager.isArabic ? "المحطة" : "Station";
  static String bus() => DataManager.isArabic ? "الحافلة" : "Bus";
  static String stationNumber() =>
      DataManager.isArabic ? "رقم المحطة" : "Numero de station";
  static String busNumber() =>
      DataManager.isArabic ? "رقم الحافلة" : "Numero de Bus";
  static String selectionee() =>
      DataManager.isArabic ? "إختر الرقم" : "Selectioner";
  static String noNews() =>
      DataManager.isArabic
          ? "لا يوجد أخبار اليوم"
          : "Pas de nouvelles aujourd'hui";
  static String mesRapports() =>
      DataManager.isArabic ? "بلاغاتي" : "Mes rapports";
  static String attente() => DataManager.isArabic ? "بلاغاتي" : "Mes rapports";

  static String getGps() =>
      DataManager.isArabic ? "موقع جغرافي" : "Localisation";

  // NEW ADDITIONS FOR TOURISM AND MAP PAGES

  // Navigation
  static String navHome() => DataManager.isArabic ? "الرئيسية" : "Accueil";
  static String navNews() => DataManager.isArabic ? "الأخبار" : "Actualité";
  static String navMap() => DataManager.isArabic ? "الخريطة" : "Carte";
  static String navTourism() => DataManager.isArabic ? "السياحة" : "Tourisme";

  // Map Page
  static String mapTitle() =>
      DataManager.isArabic ? "خريطة أكادير" : "Carte d'Agadir";
  static String agadirMorocco() =>
      DataManager.isArabic ? "أكادير، المغرب" : "Agadir, Maroc";
  static String discoverPlaces() =>
      DataManager.isArabic
          ? "اكتشف الأماكن التي لا يمكن تفويتها"
          : "Découvrez les lieux incontournables";
  static String touristPlaces() =>
      DataManager.isArabic ? "الأماكن السياحية" : "Lieux touristiques";
  static String viewOnMap() =>
      DataManager.isArabic ? "عرض على الخريطة" : "Voir sur la carte";

  // Tourism Page
  static String tourismTitle() =>
      DataManager.isArabic ? "سياحة أكادير" : "Tourisme Agadir";
  static String discoverAgadir() =>
      DataManager.isArabic ? "اكتشف أكادير" : "Découvrez Agadir";
  static String pearlOfSouth() =>
      DataManager.isArabic ? "لؤلؤة جنوب المغرب" : "La perle du Sud du Maroc";

  // Tourism Categories
  static String beachesAndWaterSports() =>
      DataManager.isArabic
          ? "الشواطئ والرياضات المائية"
          : "Plages et Sports Nautiques";
  static String beachesDescription() =>
      DataManager.isArabic
          ? "تشتهر أكادير بـ 300 يوم من أشعة الشمس في السنة وشاطئها الرائع الذي يمتد لـ 10 كم. مثالية لركوب الأمواج والإبحار وجميع الرياضات المائية."
          : "Agadir est réputée pour ses 300 jours de soleil par an et sa magnifique plage de 10 km. Idéale pour le surf, la voile et tous les sports nautiques.";

  static String cultureAndHistory() =>
      DataManager.isArabic ? "الثقافة والتاريخ" : "Culture et Histoire";
  static String cultureDescription() =>
      DataManager.isArabic
          ? "اكتشف التراث الأمازيغي الغني والتاريخ الرائع لأكادير، من القصبة التاريخية إلى المتاحف الحديثة."
          : "Découvrez l'riche héritage amazigh et l'histoire fascinante d'Agadir, de la Kasbah historique aux musées modernes.";

  static String gastronomy() =>
      DataManager.isArabic ? "فن الطبخ" : "Gastronomie";
  static String gastronomyDescription() =>
      DataManager.isArabic
          ? "استمتع بالمأكولات المغربية الأصيلة والتخصصات المحلية في المطاعم التقليدية والحديثة في المدينة."
          : "Savourez la cuisine marocaine authentique et les spécialités locales dans les restaurants traditionnels et modernes de la ville.";

  static String shoppingAndCrafts() =>
      DataManager.isArabic ? "التسوق والحرف اليدوية" : "Shopping et Artisanat";
  static String shoppingDescription() =>
      DataManager.isArabic
          ? "من سوق الحد التقليدي إلى المراكز التجارية الحديثة، تقدم أكادير تجربة تسوق فريدة تمزج بين التقليد والحداثة."
          : "Du traditionnel Souk El Had aux centres commerciaux modernes, Agadir offre une expérience shopping unique mêlant tradition et modernité.";

  // Tourism Highlights
  static String surfAndKitesurf() =>
      DataManager.isArabic
          ? "ركوب الأمواج والطائرة الورقية طوال السنة"
          : "Surf et kitesurf toute l'année";
  static String sandyBeach() =>
      DataManager.isArabic
          ? "10 كم من الشاطئ الرملي الناعم"
          : "10 km de plage de sable fin";
  static String waterSportsSchools() =>
      DataManager.isArabic
          ? "مدارس الرياضات المائية"
          : "Écoles de sports nautiques";

  static String kasbaAgadirOufella() =>
      DataManager.isArabic ? "قصبة أكادير أوفلا" : "Kasbah Agadir Oufella";
  static String amazighMuseum() =>
      DataManager.isArabic
          ? "متحف التراث الأمازيغي البلدي"
          : "Musée municipal du patrimoine amazigh";
  static String modernArchitecture() =>
      DataManager.isArabic
          ? "العمارة الحديثة ما بعد الزلزال"
          : "Architecture moderne post-séisme";

  static String freshFish() =>
      DataManager.isArabic
          ? "أسماك طازجة من الميناء"
          : "Poissons frais du port";
  static String traditionalTajines() =>
      DataManager.isArabic
          ? "طاجين وكسكس تقليدي"
          : "Tajines et couscous traditionnels";
  static String moroccanPastries() =>
      DataManager.isArabic ? "حلويات مغربية" : "Pâtisseries marocaines";

  static String soukElHad() =>
      DataManager.isArabic
          ? "سوق الحد (6000 محل)"
          : "Souk El Had (6000 boutiques)";
  static String berberCrafts() =>
      DataManager.isArabic
          ? "حرف يدوية بربرية أصيلة"
          : "Artisanat berbère authentique";
  static String modernMalls() =>
      DataManager.isArabic
          ? "مراكز تجارية حديثة"
          : "Centres commerciaux modernes";

  // Location Names and Descriptions
  static String agadirBeach() =>
      DataManager.isArabic ? "شاطئ أكادير" : "Plage d'Agadir";
  static String agadirBeachDesc() =>
      DataManager.isArabic
          ? "شاطئ رملي ناعم رائع يمتد على 10 كم على طول المحيط الأطلسي. مثالي للسباحة وركوب الأمواج والرياضات المائية."
          : "Magnifique plage de sable fin s'étendant sur 10 km le long de l'océan Atlantique. Parfaite pour la baignade, le surf et les sports nautiques.";

  static String soukElHadName() =>
      DataManager.isArabic ? "سوق الحد" : "Souk El Had";
  static String soukElHadDesc() =>
      DataManager.isArabic
          ? "أكبر سوق تقليدي في أكادير مع أكثر من 3000 محل. اكتشف الحرف اليدوية المحلية والتوابل والمنتجات التقليدية."
          : "Le plus grand marché traditionnel d'Agadir avec plus de 3000 boutiques. Découvrez l'artisanat local, les épices et les produits traditionnels.";

  static String agadirMarina() =>
      DataManager.isArabic ? "مارينا أكادير" : "Marina d'Agadir";
  static String agadirMarinaDesc() =>
      DataManager.isArabic
          ? "ميناء ترفيهي حديث مع مطاعم ومقاهي ومحلات. مثالي للتنزه المسائي مع إطلالة على المحيط."
          : "Port de plaisance moderne avec restaurants, cafés et boutiques. Parfait pour une promenade en soirée avec vue sur l'océan.";

  static String kasbaAgadirOufellaName() =>
      DataManager.isArabic ? "قصبة أكادير أوفلا" : "Kasbah Agadir Oufella";
  static String kasbaAgadirOufellaDesc() =>
      DataManager.isArabic
          ? "آثار تاريخية تقع على تلة تقدم إطلالة بانورامية استثنائية على المدينة والمحيط."
          : "Ruines historiques perchées sur une colline offrant une vue panoramique exceptionnelle sur la ville et l'océan.";

  static String valleyOfBirds() =>
      DataManager.isArabic ? "وادي الطيور" : "Vallée des Oiseaux";
  static String valleyOfBirdsDesc() =>
      DataManager.isArabic
          ? "حديقة حيوانات وحديقة نباتية في قلب المدينة. مثالية للنزهات العائلية وسط الطبيعة."
          : "Parc zoologique et jardin botanique au cœur de la ville. Idéal pour une balade familiale au milieu de la nature.";

  static String crocoparcAgadir() =>
      DataManager.isArabic ? "كروكوبارك أكادير" : "Crocoparc Agadir";
  static String crocoparcAgadirDesc() =>
      DataManager.isArabic
          ? "حديقة حيوانات فريدة تضم أكثر من 300 تمساح نيلي في بيئة استوائية استثنائية."
          : "Parc animalier unique abritant plus de 300 crocodiles du Nil dans un cadre tropical exceptionnel.";

  static String golfDuSoleil() =>
      DataManager.isArabic ? "غولف الشمس" : "Golf du Soleil";
  static String golfDuSoleilDesc() =>
      DataManager.isArabic
          ? "ملعب غولف 18 حفرة صممه روبرت ترينت جونز، يوفر إطلالة خلابة على الأطلس والمحيط."
          : "Parcours de golf 18 trous conçu par Robert Trent Jones, offrant une vue imprenable sur l'Atlas et l'océan.";

  static String amazighMuseumName() =>
      DataManager.isArabic
          ? "متحف التراث الأمازيغي البلدي"
          : "Musée Municipal du Patrimoine Amazigh";
  static String amazighMuseumDesc() =>
      DataManager.isArabic
          ? "اكتشف الثقافة الأمازيغية الغنية من خلال معارض الفن والحرف اليدوية والأشياء التقليدية."
          : "Découvrez la riche culture amazighe à travers des expositions d'art, d'artisanat et d'objets traditionnels.";
}
