import 'package:app_settings/app_settings.dart';
import 'package:flutter/material.dart';
import 'package:location/location.dart';
import 'package:marafik_app/Models/data_manager.dart';
import 'package:marafik_app/Models/lang.dart';

class LocationHandel {
  static LocationData? locationData;
  static cachLocation() async {
    Location location = Location();
    locationData = await location.getLocation();
  }

  static Future<bool> requestLocationService(BuildContext context) async {
    Location location = Location();
    bool _serviceEnabled;
    bool _permission = false;
    PermissionStatus _permissionGranted;
    int a = 0;
    _serviceEnabled = await location.serviceEnabled();
    if (!_serviceEnabled) {
      _serviceEnabled = await location.requestService();
      _serviceEnabled = true;
    }
    _permissionGranted = await location.hasPermission();
    if (_permissionGranted == PermissionStatus.denied) {
      _permissionGranted = await location.requestPermission();
      _permissionGranted = await location.hasPermission();
      if (_permissionGranted == PermissionStatus.denied ||
          _permissionGranted == PermissionStatus.deniedForever) {
        _showAppSetting(context);
      }
    } else if (_permissionGranted == PermissionStatus.deniedForever) {
      await AppSettings.openAppSettings();
    } else
      _permission = true;
    if (_serviceEnabled && _permission) {
      return true;
    }
    return (_serviceEnabled && _permission);
  }

  static String constractUrl() {
    return "https://www.google.com/maps/search/?api=1&query=${locationData!.latitude},${locationData!.longitude}";
  }

  static show(BuildContext context) {
    showDialog(
      context: context,
      builder: (_) {
        return AlertDialog(
          title: Row(
            children: [
              CircularProgressIndicator(),
              SizedBox(width: 16),
              Text(ModelLang.waitAlert()),
            ],
          ),
        );
      },
    ).then((exit) {
      if (exit == null) return;
      if (exit) {
      } else {}
    });
  }

  static _showAppSetting(BuildContext context) {
    Navigator.of(context).pop();
    showDialog(
      context: context,
      builder: (_) {
        return AlertDialog(
          title: Row(
            children: [
              MaterialButton(
                color: Colors.blue,
                onPressed: () async {
                  await AppSettings.openAppSettings();
                  Navigator.of(context).pop();
                },
              ),
              SizedBox(width: 8),
              Text("accept permission"),
            ],
          ),
        );
      },
    ).then((exit) {
      if (exit == null) return;
      if (exit) {
      } else {}
    });
  }

  static getLocation(BuildContext context, Function targetFunction) async {
    if (DataManager.location.latitude == null &&
        DataManager.location.longitude == null) {
      show(context);
      bool isLocated = await requestLocationService(context);
      if (isLocated) {
        await cachLocation();
        DataManager.location = locationData!;
        Navigator.of(context, rootNavigator: true).pop();
        targetFunction();
      } else {
        isLocated = await requestLocationService(context);
        if (isLocated) {
          await cachLocation();
          DataManager.location = locationData!;
          Navigator.of(context, rootNavigator: true).pop();
          targetFunction();
        }
      }
    } else {
      targetFunction();
    }
  }
}
