import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:marafik_app/Models/data_manager.dart';
import 'package:marafik_app/Models/lang.dart';
import 'package:marafik_app/Styles/app_style.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:flutter_sound/flutter_sound.dart';

enum RecordState { none, recording, recorded }

class AudioHandeler {
  final VoidCallback onUpdate;

  AudioHandeler({required this.onUpdate}) {
    _initRecorder();
    getFilePath();
  }

  Duration duration = const Duration(seconds: 1);
  RecordState myRecordState = RecordState.none;
  late String recordFilePath;
  int secondsPassed = 0;
  File? recordFile;
  Timer? timer;
  late Function targetSetState;

  FlutterSoundRecorder? _recorder = FlutterSoundRecorder();
  bool _isRecording = false;
  bool _isRecorderInitialized = false;

  // Initialize the recorder
  Future<void> _initRecorder() async {
    try {
      await _recorder!.openRecorder();
      _isRecorderInitialized = true;
    } catch (e) {
      print('Error initializing recorder: $e');
    }
  }

  // Dispose method to clean up resources
  void dispose() async {
    timer?.cancel();
    if (_isRecorderInitialized) {
      await _recorder!.closeRecorder();
    }
    _recorder = null;
  }

  getFilePath() async {
    recordFilePath = await getRecordFilePath();
  }

  void handleTick() {
    onUpdate();
    secondsPassed++;
  }

  void startRecord() async {
    // Check microphone permission
    if (!await Permission.microphone.isGranted) {
      PermissionStatus status = await Permission.microphone.request();
      if (status != PermissionStatus.granted) {
        print('Microphone permission denied');
        return;
      }
    }

    if ((myRecordState == RecordState.none ||
            myRecordState == RecordState.recorded) &&
        _isRecorderInitialized) {
      try {
        myRecordState = RecordState.recording;
        _isRecording = true;

        // Start recording
        await _recorder!.startRecorder(
          toFile: recordFilePath,
          codec: Codec.aacMP4, // Changed to aacMP4 for better compatibility
        );

        // Start timer
        secondsPassed = 0;
        timer = Timer.periodic(duration, (Timer t) {
          handleTick();
        });

        onUpdate();
        print('Recording started: $recordFilePath');
      } catch (e) {
        print('Error starting recording: $e');
        myRecordState = RecordState.none;
        _isRecording = false;
        onUpdate();
      }
    }
  }

  void discardRecord() async {
    if (myRecordState == RecordState.recorded ||
        myRecordState == RecordState.recording) {
      await stopRecord();
      myRecordState = RecordState.none;
    }
    await getFilePath();
    onUpdate();
  }

  Future<void> stopRecord() async {
    if (_isRecording && _isRecorderInitialized) {
      try {
        String? path = await _recorder!.stopRecorder();
        timer?.cancel();
        _isRecording = false;

        if (File(path!).existsSync()) {
          recordFile = File(path);
          myRecordState = RecordState.recorded;
          print('Recording stopped and saved: $path');
        } else {
          print('Recording failed - file not found');
          myRecordState = RecordState.none;
        }

        onUpdate();
      } catch (e) {
        print('Error stopping recording: $e');
        _isRecording = false;
        myRecordState = RecordState.none;
        timer?.cancel();
        onUpdate();
      }
    }
  }

  Future<String> getRecordFilePath() async {
    Directory storageDirectory = await getApplicationDocumentsDirectory();
    String sdPath = storageDirectory.path + "/record";
    String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    String fileName =
        "/tempRecord_$timestamp.m4a"; // Changed to .m4a for aacMP4 codec
    var directory = Directory(sdPath);
    if (!directory.existsSync()) directory.createSync(recursive: true);

    // Clean up old temp files
    try {
      List<FileSystemEntity> files = directory.listSync();
      for (var file in files) {
        if (file is File && file.path.contains('tempRecord_')) {
          file.deleteSync();
        }
      }
    } catch (e) {
      print('Error cleaning temp files: $e');
    }

    return sdPath + fileName;
  }

  void deleteAudio() async {
    if (myRecordState == RecordState.recorded ||
        myRecordState == RecordState.recording) {
      await stopRecord();
      timer?.cancel();
      secondsPassed = 0;
      myRecordState = RecordState.none;
      recordFile = null;

      // Delete the recorded file
      try {
        if (recordFile != null && recordFile!.existsSync()) {
          recordFile!.deleteSync();
        }
      } catch (e) {
        print('Error deleting audio file: $e');
      }
    }
    onUpdate();
    await getFilePath();
  }

  void startStopRecord() async {
    if (myRecordState == RecordState.none ||
        myRecordState == RecordState.recorded) {
      startRecord();
    } else if (myRecordState == RecordState.recording) {
      await stopRecord();
    }
    onUpdate();
  }

  Text? recordStateString() {
    if (myRecordState == RecordState.none) {
      return Text(
        ModelLang.noAudio(),
        style: AppStayles.textStyles(Colors.black, 17.sp, true),
      );
    } else if (myRecordState == RecordState.recording) {
      return Text(
        ModelLang.inRecorde(),
        style: AppStayles.textStyles(Colors.black, 17.sp, true),
      );
    } else if (myRecordState == RecordState.recorded) {
      return Text(
        ModelLang.stopRecorde(),
        style: AppStayles.textStyles(Colors.black, 17.sp, true),
      );
    }
    return null;
  }

  // Method to save the recorded audio to a permanent location
  Future<File?> saveRecordedAudio() async {
    if (myRecordState != RecordState.recorded || recordFile == null) {
      print('No recorded audio to save');
      return null;
    }

    try {
      if (!recordFile!.existsSync()) {
        print('Recorded file does not exist');
        return null;
      }

      // Create permanent file path
      Directory storageDirectory = await getApplicationDocumentsDirectory();
      String permanentPath = storageDirectory.path + "/record";
      String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      String fileName = "/saved_record_$timestamp.m4a";

      var directory = Directory(permanentPath);
      if (!directory.existsSync()) directory.createSync(recursive: true);

      File permanentFile = File(permanentPath + fileName);

      // Copy temp file to permanent location
      await recordFile!.copy(permanentFile.path);

      print("Audio saved to: ${permanentFile.path}");
      return permanentFile;
    } catch (e) {
      print("Error saving audio: $e");
      return null;
    }
  }
}
