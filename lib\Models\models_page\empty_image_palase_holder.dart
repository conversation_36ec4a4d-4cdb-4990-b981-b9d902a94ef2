import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:marafik_app/Models/lang.dart';
import 'package:marafik_app/Styles/app_style.dart';

class EmptyImagePlaceHolder extends StatelessWidget {
  final index;
  const EmptyImagePlaceHolder({this.index, Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Container(
          width: 100.w,
          height: 100.w,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(4.w),
            color: HexColor("#EEEEEE"),
          ),
        ),
      ],
    );
  }
}
