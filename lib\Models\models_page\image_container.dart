import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:marafik_app/Models/lang.dart';
import 'package:marafik_app/Styles/app_style.dart';

class ImageContainer extends StatefulWidget {
  final fn;
  final imageFile;
  final index;

  final String title;
  const ImageContainer(
    this.index,
    this.imageFile,
    this.title, {
    Key? key,
    this.fn,
  }) : super(key: key);

  @override
  _ImageContainerState createState() => _ImageContainerState();
}

class _ImageContainerState extends State<ImageContainer> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: 100.w,
      height: 100.w,
      child: Stack(
        children: [
          Container(
            width: 100.w,
            height: 100.w,
            decoration: BoxDecoration(
              // borderRadius: BorderRadius.circular(18.w),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(4.w),
              child: Image.file(widget.imageFile, fit: BoxFit.cover),
            ),
          ),
          Align(
            alignment: Alignment.topRight,
            child: InkWell(
              overlayColor: MaterialStateProperty.all(Colors.transparent),
              child: Container(
                child: Icon(Icons.delete, color: Colors.white, size: 18.w),
                width: 30.w,
                height: 30.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.only(
                    topRight: Radius.circular(4.w),
                    bottomLeft: Radius.circular(10.w),
                  ),
                  color: Colors.red,
                ),
              ),
              onTap: widget.fn,
            ),
          ),
        ],
      ),
    );
  }
}
