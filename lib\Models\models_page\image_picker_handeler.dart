import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'package:marafik_app/Models/data_manager.dart';
import 'package:marafik_app/Models/lang.dart';
import 'package:marafik_app/Models/location.dart';
import 'package:marafik_app/Styles/app_style.dart';
import 'dart:io';

class ImagePickerHandeler {
  ImagePickerHandeler() {
    imgList = DataManager.imageList;
  }

  List<File> imgList = [];

  late Function targetSetState;
  late BuildContext context;

  removeImage(File selectedImage) {
    imgList.remove(selectedImage);
    targetSetState(imgList.isNotEmpty);
  }

  Future pickercamera() async {
    // if (!DataManager.location) {
    //   show();
    //   LocationHandel.requestLocationService().then((result) {
    //     if (!result) {
    //       Navigator.of(context).pop();
    //       showDialog(
    //         context: context,
    //         builder: (BuildContext context) {
    //           return InfoDialog(
    //               title: ModelLang.alert(),
    //               description: ModelLang.rappelGps(),
    //               afterCancel: () {});
    //         },
    //       );
    //     } else {
    //       DataManager.location = true;
    //       LocationHandel.cachLocation();
    //       Navigator.of(context).pop();
    //       pickImage();
    //     }
    //   });
    // } else {
    LocationHandel.getLocation(context, pickImage);
    // pickImage();
    // }
    if (imgList.isNotEmpty) {
      // DataManager.canGo=true;
      targetSetState(true);
    }
  }

  pickImage() async {
    final pickedImage = await ImagePicker().pickImage(
      source: ImageSource.camera,
    );
    if (pickedImage != null) {
      imgList.add(File(pickedImage.path));
      targetSetState(true);
    }
  }

  show() {
    showDialog(
      barrierDismissible: false,
      context: context,
      builder: (_) {
        return AlertDialog(title: Text(ModelLang.waitAlert()));
      },
    ).then((exit) {
      if (exit == null) return;

      if (exit) {
        // user pressed Yes button
      } else {
        // user pressed No button
      }
    });
  }
}
