import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:marafik_app/Models/lang.dart';
import 'package:marafik_app/Pages/Home/map_page.dart';
import 'package:marafik_app/Pages/Home/news_page.dart';
import 'package:marafik_app/Pages/Home/second_page.dart';
import 'package:marafik_app/Pages/Home/tourism_page.dart';

class NavBar extends StatefulWidget {
  const NavBar({Key? key}) : super(key: key);

  @override
  _NavBarState createState() => _NavBarState();
}

class _NavBarState extends State<NavBar> {
  int _selectedIndex = 0;

  Future<bool> onWillPop() async {
    return false;
  }

  // List of pages
  final List<Widget> _pages = [
    SecondPage(),
    NewsPage(),
    MapPage(),
    TourismPage(),
  ];

  // Navigation items data - Updated to use ModelLang
  List<NavItem> get _navItems => [
    NavItem(
      icon: Icons.home_rounded,
      label: ModelLang.navHome(),
      activeColor: Color(0xFF3B82F6),
    ),
    NavItem(
      icon: Icons.article_rounded,
      label: ModelLang.navNews(),
      activeColor: Color(0xFF10B981),
    ),
    NavItem(
      icon: Icons.map_rounded,
      label: ModelLang.navMap(),
      activeColor: Color(0xFFEF4444),
    ),
    NavItem(
      icon: Icons.attractions_rounded,
      label: ModelLang.navTourism(),
      activeColor: Color(0xFF8B5CF6),
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return SafeArea(
      child: WillPopScope(
        onWillPop: onWillPop,
        child: Scaffold(
          backgroundColor: Colors.grey[50],
          appBar: _buildAppBar(),
          body: IndexedStack(index: _selectedIndex, children: _pages),
          bottomNavigationBar: _buildBottomNavBar(),
        ),
      ),
    );
  }

  PreferredSizeWidget? _buildAppBar() {
    // Only show app bar for News, Map and Tourism pages
    if (_selectedIndex == 1) {
      return AppBar(
        forceMaterialTransparency: true,
        toolbarHeight: 65.h,
        elevation: 0,
        centerTitle: true,
        title: Text(
          ModelLang.news(),
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
      );
    } else if (_selectedIndex == 2) {
      return AppBar(
        forceMaterialTransparency: true,
        toolbarHeight: 65.h,
        elevation: 0,
        centerTitle: true,
        title: Text(
          ModelLang.mapTitle(),
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
      );
    } else if (_selectedIndex == 3) {
      return AppBar(
        toolbarHeight: 65.h,
        backgroundColor: Colors.white,
        elevation: 0.5,
        centerTitle: true,
        title: Text(
          ModelLang.tourismTitle(),
          style: TextStyle(
            fontSize: 18.sp,
            fontWeight: FontWeight.w600,
            color: Colors.black87,
          ),
        ),
      );
    }
    return null; // No app bar for home page
  }

  Widget _buildBottomNavBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            offset: Offset(0, -5),
          ),
        ],
      ),
      child: SafeArea(
        child: Container(
          height: 70.h,
          padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 6.h),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children:
                _navItems.asMap().entries.map((entry) {
                  int index = entry.key;
                  NavItem item = entry.value;
                  bool isSelected = _selectedIndex == index;

                  return Expanded(
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          _selectedIndex = index;
                        });
                      },
                      child: Container(
                        margin: EdgeInsets.symmetric(horizontal: 2.w),
                        padding: EdgeInsets.symmetric(
                          horizontal: 8.w,
                          vertical: 6.h,
                        ),
                        decoration: BoxDecoration(
                          color:
                              isSelected
                                  ? item.activeColor.withOpacity(0.1)
                                  : Colors.transparent,
                          borderRadius: BorderRadius.circular(12.r),
                        ),
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              padding: EdgeInsets.all(6.w),
                              decoration: BoxDecoration(
                                color:
                                    isSelected
                                        ? item.activeColor
                                        : Colors.transparent,
                                borderRadius: BorderRadius.circular(8.r),
                              ),
                              child: Icon(
                                item.icon,
                                size: 20.sp,
                                color:
                                    isSelected
                                        ? Colors.white
                                        : Colors.grey[600],
                              ),
                            ),
                            SizedBox(height: 2.h),
                            Text(
                              item.label,
                              style: TextStyle(
                                fontSize: 10.sp,
                                fontWeight:
                                    isSelected
                                        ? FontWeight.w600
                                        : FontWeight.w500,
                                color:
                                    isSelected
                                        ? item.activeColor
                                        : Colors.grey[600],
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }).toList(),
          ),
        ),
      ),
    );
  }
}

// Navigation item model
class NavItem {
  final IconData icon;
  final String label;
  final Color activeColor;

  NavItem({required this.icon, required this.label, required this.activeColor});
}
