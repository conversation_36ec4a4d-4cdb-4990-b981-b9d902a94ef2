import 'package:permission_handler/permission_handler.dart';

class Permition {
  static Future<bool> getPermitions() async {
    if (!getPermitionsStatus()) return true;
    Map<Permission, PermissionStatus> statuses = await [
      Permission.location,
      Permission.storage,
      Permission.camera,
      Permission.microphone,
    ].request();

    bool isGranted = true;

    statuses.forEach((key, value) {
      if (!value.isGranted) {
        isGranted = false;
        return;
      }
    });
    return isGranted;
  }

  static bool getPermitionsStatus() {
    bool isGranted = true;
    [
      Permission.location,
      Permission.storage,
      Permission.camera,
      Permission.microphone,
    ].forEach((element) async {
      if (await element.isGranted == false) {
        isGranted = false;

        return;
      }
    });
    return isGranted;
  }
}
