import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:marafik_app/Models/data_manager.dart';
import 'package:marafik_app/Styles/app_style.dart';

class ProgressBar extends StatefulWidget {
  const ProgressBar({super.key});

  @override
  _ProgressBarState createState() => _ProgressBarState();
}

class _ProgressBarState extends State<ProgressBar> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    DataManager.progressSetState = sets;
  }

  sets() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        Container(
          width: MediaQuery.of(context).size.width,
          height: 8.h,
          decoration: BoxDecoration(boxShadow: shadow(), color: Colors.white),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              AnimatedContainer(
                duration: Duration(milliseconds: 300),
                width:
                    MediaQuery.of(context).size.width *
                    DataManager.curentProgress /
                    100.0,
                height: 8.h,
                color: AppStayles.fluttertoastBackgroundHexa.withOpacity(0.5),
              ),
            ],
          ),
        ),
      ],
    );
  }
}

dynamic shadow() {
  return [
    BoxShadow(
      color: Color.fromARGB(5, 0, 0, 0),
      offset: Offset(0, 1.0),
      spreadRadius: 1,
      blurRadius: 1,
    ),
  ];
}
