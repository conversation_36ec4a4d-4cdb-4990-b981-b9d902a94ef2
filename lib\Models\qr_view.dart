import 'package:flutter/material.dart';
import 'package:marafik_app/Models/data_manager.dart';
import 'package:marafik_app/Models/lang.dart';
import 'package:marafik_app/Pages/choix_page.dart';
import 'package:marafik_app/Pages/qr_page.dart';
import 'package:marafik_app/Styles/app_style.dart';
import 'package:marafik_app/database/info_aswaq.dart';
import 'package:marafik_app/database/sqlite.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';

class QrView extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => _QrViewState();
}

class _QrViewState extends State<QrView> {
  Barcode? result;
  QRViewController? controller;
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  ////
  Future<bool> onWillPop() async {
    controller!.pauseCamera();
    AppStayles.navigateTo(context, QrCodePage());
    return false;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: onWillPop,
      child: Scaffold(
        body: Column(
          children: <Widget>[
            Container(child: Expanded(flex: 4, child: _buildQrView(context))),
          ],
        ),
      ),
    );
  }

  Widget _buildQrView(BuildContext context) {
    var scanArea =
        (MediaQuery.of(context).size.width < 400 ||
                MediaQuery.of(context).size.height < 400)
            ? 250.0 //150.0
            : 300.0;
    return QRView(
      key: qrKey,
      onQRViewCreated: _onQRViewCreated,
      overlay: QrScannerOverlayShape(
        borderColor: Colors.red,
        borderRadius: 10,
        borderLength: 30,
        borderWidth: 10,
        cutOutSize: scanArea,
      ),
    );
  }

  Future<void> _onQRViewCreated(QRViewController controller) async {
    setState(() {
      this.controller = controller;
    });
    controller.scannedDataStream.listen((scanData) async {
      setState(() {
        DataManager.qrCode = '';
        result = scanData;
        controller.pauseCamera();
        DataManager.qrCode = result!.code!;
        print(result!.code);
        if (DataManager.qrCode.contains("www.sdmb.ma/ibin/")) {
          DataManager.qrCode = DataManager.qrCode.substring(17, 27).toString();
        }
      });
      DbHelper db = DbHelper();
      if (DataManager.idSector == 1) {
        // if (await dbGetOwner(DataManager.qrCode) == null) {
        // AppStayles.navigateTo(context, QrCodePage());
        // AppStayles.showAlertDialogSimple(
        //   context,
        //   ModelLang.ok(),
        //   ModelLang.qrState(),
        //   ModelLang.qrFalse(),
        //   false,
        // );
        // } else {
        AppStayles.navigateTo(context, ChoixPage());
        // }
      } else if (DataManager.idSector == 2) {
        if (await db.getTrashByQr(DataManager.qrCode) == null) {
          AppStayles.navigateTo(context, QrCodePage());
          AppStayles.showAlertDialogSimple(
            context,
            ModelLang.ok(),
            ModelLang.qrState(),
            ModelLang.qrFalse(),
            false,
          );
        } else {
          AppStayles.navigateTo(context, ChoixPage());
        }
      } else if (DataManager.idSector == 5) {
        if (!await db.isExistBatoire(DataManager.qrCode)) {
          AppStayles.navigateTo(context, QrCodePage());
          AppStayles.showAlertDialogSimple(
            context,
            ModelLang.ok(),
            ModelLang.qrState(),
            ModelLang.qrFalse(),
            false,
          );
        } else {
          AppStayles.navigateTo(context, ChoixPage());
        }
      }
    });
  }

  Future<String?> dbGetOwner(String code) async {
    DbHelper db = DbHelper();
    Shop shop = await db.getInfoAswaqByShopeQR(code);
    return shop.colQrOwner;
    }

  @override
  void dispose() {
    controller?.dispose();
    super.dispose();
  }
}
