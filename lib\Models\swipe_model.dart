import 'package:flutter/material.dart';
import 'package:liquid_swipe/liquid_swipe.dart';
import 'package:marafik_app/Models/check_connection.dart';
import 'package:marafik_app/Models/lang.dart';
import 'package:marafik_app/Models/navbar.dart';
import 'package:marafik_app/Models/permission_handler.dart';
import 'package:marafik_app/Pages/first_page.dart';
import 'package:marafik_app/Styles/app_style.dart';
import 'package:marafik_app/database/info_aswaq.dart';
import 'package:marafik_app/database/sqlite.dart';

class FirstModel extends StatefulWidget {
  const FirstModel({Key? key}) : super(key: key);

  @override
  _FirstModelState createState() => _FirstModelState();
}

class _FirstModelState extends State<FirstModel> {
  final pages = [FirstPage(), NavBar()];

  bool t = true;
  @override
  void initState() {
    // TODO: implement initState
    // FetchData.startFetchingDataTest2();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    Future<bool> onWillPop() async {
      return false;
    }

    Future<bool> isFilled() async {
      DbHelper db = DbHelper();
      List<Shop> s = await db.getAllInfoAswaq();
      if (s.isEmpty && !await CheckConnection.isConnected()) {
        return false;
      }
      return true;
    }

    return WillPopScope(
      onWillPop: onWillPop,
      child: Scaffold(
        body: Builder(
          builder:
              (context) =>
                  t
                      ? LiquidSwipe(
                        onPageChangeCallback: (h) async {
                          try {
                            getPermition(context);
                            if (await isFilled()) {
                              Navigator.push(
                                context,
                                PageRouteBuilder(
                                  maintainState: false,
                                  transitionDuration: Duration(milliseconds: 1),
                                  transitionsBuilder: (
                                    context,
                                    animation,
                                    animationTime,
                                    child,
                                  ) {
                                    animation = CurvedAnimation(
                                      parent: animation,
                                      curve: Curves.fastLinearToSlowEaseIn,
                                    );
                                    return ScaleTransition(
                                      alignment: Alignment.center,
                                      scale: animation,
                                      child: child,
                                    );
                                  },
                                  pageBuilder: (
                                    context,
                                    animation,
                                    secondaryAnimation,
                                  ) {
                                    return NavBar();
                                  },
                                ),
                              );
                            } else {
                              AppStayles.showAlertDialogSimple(
                                context,
                                ModelLang.ok(),
                                ModelLang.connectionState(),
                                ModelLang.noCnx(),
                                true,
                              );
                            }
                          } catch (e) {}
                        },
                        pages: pages,
                      )
                      : SizedBox.shrink(),
        ),
      ),
    );
  }
}

void getPermition(BuildContext context) async {
  if (await Permition.getPermitions() == false) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return InfoDialog(
          title: ModelLang.alert(),
          description: ModelLang.rappelAutorisations(),
          afterCancel: () {
            getPermition(context);
          },
        );
      },
    );
  }
}
