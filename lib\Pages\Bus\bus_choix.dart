import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:marafik_app/Models/data_manager.dart';
import 'package:marafik_app/Models/display_module.dart';
import 'package:marafik_app/Models/lang.dart';
import 'package:marafik_app/Models/navbar.dart';
import 'package:marafik_app/Pages/Bus/bus_number.dart';
import 'package:marafik_app/Pages/gps_page.dart';
import 'package:marafik_app/Styles/app_style.dart';

class BusChoose extends StatefulWidget {
  @override
  _BusChooseState createState() => _BusChooseState();
}

class _BusChooseState extends State<BusChoose> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    DataManager.previouspage = NavBar();
    DataManager.canGo = false;
    DataManager.footerText = ModelLang.busChoix();

    Future.delayed(Duration(milliseconds: 500), () {
      DataManager.updateProgress(0);
    });
  }

  Future<bool> _onWillPop() async {
    AppStayles.navigateTo(context, NavBar());
    return false;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: DisplayModule(
        Center(
          child: Container(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                myButton(
                  Icons.storefront_outlined,
                  ModelLang.station(),
                  1,
                  context,
                  true,
                ),
                SizedBox(width: 1.w, height: 12.h),
                myButton(
                  Icons.directions_bus_filled_outlined,
                  ModelLang.bus(),
                  2,
                  context,
                  false,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

Widget myButton(
  IconData icon,
  String txt,
  int ontap,
  BuildContext context,
  bool isStat,
) {
  return Container(
    width: 150.w,
    height: MediaQuery.of(context).size.width * 0.20,
    decoration: BoxDecoration(
      borderRadius: BorderRadius.circular(4.w),
      gradient: AppStayles.colorGradient(),
    ),
    child: InkWell(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(icon, size: 32.w, color: Colors.white),
          SizedBox(width: 5.w, height: 1.h),
          Container(
            width: 90.w,
            // color: Colors.amber,
            child: Center(
              child: Text(
                txt,
                textAlign: TextAlign.center,
                style: AppStayles.textStyles(Colors.white, 25.sp, false),
              ),
            ),
          ),
        ],
      ),
      onTap: () {
        if (ontap == 1) {
          AppStayles.navigateTo(context, GpsPage());
        } else {
          AppStayles.navigateTo(context, BusNumber());
        }
      },
    ),
  );
}
