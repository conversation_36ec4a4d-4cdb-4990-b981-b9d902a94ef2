import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:marafik_app/Models/data_manager.dart';
import 'package:marafik_app/Models/display_module.dart';
import 'package:marafik_app/Models/lang.dart';
import 'package:marafik_app/Pages/Bus/bus_choix.dart';
import 'package:marafik_app/Pages/choix_page.dart';
import 'package:marafik_app/Styles/app_style.dart';
import 'package:marafik_app/database/info_trans.dart';
import 'package:marafik_app/database/sqlite.dart';

class BusNumber extends StatefulWidget {
  @override
  _BusNumberState createState() => _BusNumberState();
}

class _BusNumberState extends State<BusNumber> {
  String dropdownValue = '';
  List<String> lsData = [];
  Future<void> filldata() async {
    DbHelper db = DbHelper();
    List<InfoTrans> data = await db.getAlltableInfoTrans();
    data.forEach((element) {
      setState(() {
        lsData.add(element.busNum);
      });
    });
    print(lsData);
  }

  myNextFunction() {
    AppStayles.navigateTo(context, ChoixPage());
  }

  @override
  void initState() {
    super.initState();
    filldata();
    DataManager.previouspage = BusChoose();
    DataManager.canGo = true;
    DataManager.footerText = ModelLang.busNumber();
    DataManager.myNextAction = myNextFunction;
    Future.delayed(Duration(milliseconds: 500), () {
      DataManager.updateProgress(10);
    });
  }

  @override
  Widget build(BuildContext context) {
    return DisplayModule(
      Center(
        child: Container(
          // color: Colors.amber,
          height: 460.h,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              SizedBox(height: 50.h),
              Container(
                width: 250.w,
                height: 50.h,
                decoration: BoxDecoration(
                  color: Colors.redAccent,
                  borderRadius: BorderRadius.all(Radius.circular(4.w)),
                  gradient: LinearGradient(
                    begin: Alignment.topRight,
                    end: Alignment.bottomLeft,
                    colors: [HexColor("#EC823A"), HexColor("#EC823A")],
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 238.w,
                      height: 36.h,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.all(Radius.circular(2.w)),
                      ),
                      child: DropdownButton<String>(
                        underline: Container(color: Colors.transparent),
                        hint: Center(
                          child: Text(
                            ModelLang.selectionee(),
                            style: AppStayles.textStyles(
                              Colors.black54,
                              15.sp,
                              false,
                            ),
                          ),
                        ),
                        value: dropdownValue != '' ? dropdownValue : null,
                        isExpanded: true,
                        onChanged: (data) async {
                          setState(() {
                            DataManager.canGo = true;
                            DataManager.numBus = int.parse(data!);
                            dropdownValue = data;
                          });
                          print("ligne" + data!);
                        },
                        items:
                            lsData.map<DropdownMenuItem<String>>((
                              String value,
                            ) {
                              return DropdownMenuItem<String>(
                                value: value,
                                child: Center(child: Text(value)),
                              );
                            }).toList(),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
