import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:marafik_app/Models/data_manager.dart';
import 'package:marafik_app/Models/lang.dart';
import 'package:marafik_app/Styles/app_style.dart';
import 'package:marafik_app/database/sqlite.dart';

class HistoryPage extends StatefulWidget {
  const HistoryPage({Key? key}) : super(key: key);

  @override
  _HistoryPageState createState() => _HistoryPageState();
}

class _HistoryPageState extends State<HistoryPage> {
  DbHelper db = DbHelper();
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  String secteur(id) {
    switch (id) {
      case 1:
        return ModelLang.pcp();
      case 2:
        return ModelLang.wms();
      case 3:
        return ModelLang.transport();
      case 4:
        return ModelLang.espace();
      case 5:
        return ModelLang.batoire();
      case 6:
        return ModelLang.eauAssain();
      case 7:
        return ModelLang.voirie();
      case 8:
        return ModelLang.securite();

      default:
        return ModelLang.pcp();
    }
  }

  String myImage(id) {
    switch (id) {
      case 1:
        return "pcp-icon.png";
      case 2:
        return "wms-icon.png";
      case 3:
        return "bus-icon.png";
      case 4:
        return "espace-icon.png";
      case 5:
        return "bat-icon.png";
      case 6:
        return "water_pipe.png";
      case 7:
        return "road.png";
      case 8:
        return "security.png";
      default:
        return "pcp-icon.png";
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      // color: Colors.grey[200],
      // height: 180.h,
      child:
          DataManager.listRequest.length == 0
              ? Container(
                width: 500.w,
                height: 100.h,
                child: Center(
                  child: Text(
                    ModelLang.noRaports(),
                    style: AppStayles.textStyles(Colors.black, 20.sp, true),
                  ),
                ),
              )
              : ListView.builder(
                shrinkWrap: true,
                // padding: EdgeInsets.zero,
                physics: ClampingScrollPhysics(),
                itemCount: DataManager.listRequest.length,
                itemBuilder: (context, index) {
                  return Column(
                    children: [
                      // SizedBox(
                      //   height: 120.h,
                      // ),
                      myCard(
                        context,
                        myImage(DataManager.listRequest[index].idSector),
                        secteur(
                          DataManager.listRequest[index].idSector,
                        ).toString(),
                        DataManager.listRequest[index].date,
                        DataManager.listRequest[index].state,
                      ),
                      index == DataManager.listRequest.length - 1
                          ? SizedBox(height: 50.h)
                          : Text(""),
                    ],
                  );
                },
              ),
    );
  }
}

myCard(
  BuildContext context,
  String image,
  String typePane,
  String date,
  String statu,
) {
  return Container(
    decoration: BoxDecoration(
      color: Colors.white60,
      border: Border(bottom: BorderSide(width: 1, color: Colors.grey)),
    ),
    height: 95.h,
    child: Row(
      // mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: MediaQuery.of(context).size.width * 0.3,
          // color: Colors.red,k
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 70.w,
                height: 70.w,
                // padding: EdgeInsets.all(5.w),
                decoration: BoxDecoration(
                  // border: Border.all(width: 1, color: Colors.grey),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      width: 60.w,
                      height: 60.w,
                      decoration: BoxDecoration(
                        // color: Colors.black,
                        image: DecorationImage(
                          image: AssetImage("assets/" + image),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        Container(
          child: Column(
            children: [
              Container(
                width: MediaQuery.of(context).size.width * 0.6,
                // color: Colors.blue,
                height: 35.h,
                // padding: EdgeInsets.only(
                //     top: DataManager.isArabic ? 12.h : 18.h, left: 1.w),
                child:
                    DataManager.isArabic
                        ? Center(
                          child: Text(
                            ModelLang.reclamation() + " " + typePane,
                            style: TextStyle(
                              fontSize: 16.sp,
                              fontWeight: FontWeight.bold,
                              overflow: TextOverflow.visible,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        )
                        : Center(
                          child: Text(
                            typePane + " " + ModelLang.reclamation(),
                            textAlign: TextAlign.center,
                            style: TextStyle(
                              fontSize: 15.sp,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
              ),
              Row(
                children: [
                  Container(
                    height: 45.h,
                    padding: EdgeInsets.only(
                      top: DataManager.isArabic ? 17.h : 21.h,
                      left: 10.w,
                    ),
                    width: 145.w,
                    // color: Colors.black,
                    child: (Text(
                      statu,
                      textAlign: TextAlign.center,
                      style: AppStayles.textStyles(Colors.black54, 12.sp, true),
                    )),
                  ),
                  // SizedBox(
                  //   width: 30.w,
                  // ),
                  Container(
                    height: 45.h,
                    width: 89.w,
                    padding: EdgeInsets.only(
                      top: DataManager.isArabic ? 20.h : 21.h,
                      left: 10.w,
                    ),
                    // color: Colors.black,
                    child: (Text(
                      date,
                      textAlign: TextAlign.center,
                      style: AppStayles.textStyles(Colors.black54, 12.sp, true),
                    )),
                  ),
                ],
              ),
            ],
          ),
        ),
      ],
    ),
  );
}
