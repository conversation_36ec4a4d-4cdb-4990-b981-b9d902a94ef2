import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:marafik_app/helpers/utils.dart';
import 'package:marafik_app/Models/lang.dart';

class MapPage extends StatelessWidget {
  const MapPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.grey[50],
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header section
            Container(
              width: double.infinity,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage('assets/agadir.jpeg'),
                  fit: BoxFit.cover,
                ),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [Color(0xFF60A5FA), Color(0xFF3B82F6)],
                ),
              ),
              child: Safe<PERSON>rea(
                child: Padding(
                  padding: EdgeInsets.all(20.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.location_on_rounded,
                            color: Colors.white,
                            size: 28.sp,
                          ),
                          SizedBox(width: 12.w),
                          Text(
                            ModelLang.agadirMorocco(),
                            style: TextStyle(
                              fontSize: 24.sp,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 8.h),
                      Text(
                        ModelLang.discoverPlaces(),
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: Colors.white.withOpacity(0.9),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            // Locations grid
            Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    ModelLang.touristPlaces(),
                    style: TextStyle(
                      fontSize: 20.sp,
                      fontWeight: FontWeight.w700,
                      color: Colors.black87,
                    ),
                  ),
                  SizedBox(height: 16.h),

                  // Grid of location cards
                  GridView.builder(
                    shrinkWrap: true,
                    physics: NeverScrollableScrollPhysics(),
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 1,
                      childAspectRatio: _getCardAspectRatio(context),
                      mainAxisSpacing: 12.h,
                    ),
                    itemCount: agadirLocations.length,
                    itemBuilder: (context, index) {
                      return LocationCard(location: agadirLocations[index]);
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Responsive aspect ratio based on screen size
double _getCardAspectRatio(BuildContext context) {
  double screenWidth = MediaQuery.of(context).size.width;
  double screenHeight = MediaQuery.of(context).size.height;

  if (screenHeight < 700) return 4; // Small screens
  if (screenWidth > 400.w) return 5; // Larger phones/tablets
  return 2.8; // Default for most phones
}

class LocationCard extends StatelessWidget {
  final AgadirLocation location;

  const LocationCard({Key? key, required this.location}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap:
          () => Utils.showMapOptions(
            location.latitude,
            location.longitude,
            context,
          ),
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16.r),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.08),
              blurRadius: 10,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Row(
          children: [
            // Image section
            Container(
              width: 100.w,
              height: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(16.r),
                  bottomLeft: Radius.circular(16.r),
                ),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: location.gradient,
                ),
              ),
              child: Stack(
                children: [
                  // Background pattern
                  Positioned.fill(
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(16.r),
                          bottomLeft: Radius.circular(16.r),
                        ),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(16.r),
                          bottomLeft: Radius.circular(16.r),
                        ),
                        child:
                            location.imagePath != null
                                ? Image.asset(
                                  location.imagePath!,
                                  fit: BoxFit.cover,
                                )
                                : Container(
                                  decoration: BoxDecoration(
                                    gradient: LinearGradient(
                                      begin: Alignment.topLeft,
                                      end: Alignment.bottomRight,
                                      colors: location.gradient,
                                    ),
                                  ),
                                ),
                      ),
                    ),
                  ),
                  // Icon overlay
                  Center(
                    child: Container(
                      padding: EdgeInsets.all(12.w),
                      decoration: BoxDecoration(
                        color: Colors.black.withOpacity(0.3),
                        borderRadius: BorderRadius.circular(50.r),
                      ),
                      child: Icon(
                        location.icon,
                        color: Colors.white,
                        size: 24.sp,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // Content section
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(12.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    // Title and description
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            location.name,
                            style: TextStyle(
                              fontSize: 14.sp,
                              fontWeight: FontWeight.w700,
                              color: Colors.black87,
                              height: 1.2,
                            ),
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                          SizedBox(height: 6.h),
                          Expanded(
                            child: Text(
                              location.description,
                              style: TextStyle(
                                fontSize: 11.sp,
                                color: Colors.grey[600],
                                height: 1.3,
                              ),
                              maxLines: 4,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Action button
                    Container(
                      margin: EdgeInsets.only(top: 8.h),
                      child: Row(
                        children: [
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: 10.w,
                              vertical: 6.h,
                            ),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: location.gradient,
                              ),
                              borderRadius: BorderRadius.circular(16.r),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.navigation_rounded,
                                  color: Colors.white,
                                  size: 12.sp,
                                ),
                                SizedBox(width: 4.w),
                                Text(
                                  ModelLang.viewOnMap(),
                                  style: TextStyle(
                                    fontSize: 10.sp,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.white,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

// Data model
class AgadirLocation {
  final String name;
  final String description;
  final IconData icon;
  final List<Color> gradient;
  final double latitude;
  final double longitude;
  final String? imagePath; // Optional image path

  AgadirLocation({
    required this.name,
    required this.description,
    required this.icon,
    required this.gradient,
    required this.latitude,
    required this.longitude,
    this.imagePath,
  });
}

// Static data with real coordinates - Updated to use ModelLang
final List<AgadirLocation> agadirLocations = [
  AgadirLocation(
    name: ModelLang.agadirBeach(),
    description: ModelLang.agadirBeachDesc(),
    icon: Icons.beach_access_rounded,
    gradient: [Color(0xFF60A5FA), Color(0xFF3B82F6)],
    latitude: 30.417118,
    longitude: -9.606687,
    imagePath: 'assets/plage.jpg',
  ),
  AgadirLocation(
    name: ModelLang.soukElHadName(),
    description: ModelLang.soukElHadDesc(),
    icon: Icons.shopping_bag_rounded,
    gradient: [Color(0xFF34D399), Color(0xFF10B981)],
    latitude: 30.412244,
    longitude: -9.577185,
    imagePath: 'assets/Souk El Had.jpeg',
  ),
  AgadirLocation(
    name: ModelLang.agadirMarina(),
    description: ModelLang.agadirMarinaDesc(),
    icon: Icons.sailing_rounded,
    gradient: [Color(0xFFF87171), Color(0xFFEF4444)],
    latitude: 30.426299,
    longitude: -9.560515,
    imagePath: 'assets/marina-agadir.jpg',
  ),
  AgadirLocation(
    name: ModelLang.kasbaAgadirOufellaName(),
    description: ModelLang.kasbaAgadirOufellaDesc(),
    icon: Icons.castle_rounded,
    gradient: [Color(0xFFA78BFA), Color(0xFF8B5CF6)],
    latitude: 30.4244851,
    longitude: -9.6114672,
    imagePath: 'assets/oufella.jpg',
  ),
  AgadirLocation(
    name: ModelLang.valleyOfBirds(),
    description: ModelLang.valleyOfBirdsDesc(),
    icon: Icons.park_rounded,
    gradient: [Color(0xFF4ADE80), Color(0xFF22C55E)],
    latitude: 30.42018,
    longitude: -9.59815,
    imagePath: 'assets/vallee-des-oiseaux.jpg',
  ),
  AgadirLocation(
    name: ModelLang.crocoparcAgadir(),
    description: ModelLang.crocoparcAgadirDesc(),
    icon: Icons.pets_rounded,
    gradient: [Color(0xFFFBBF24), Color(0xFFF59E0B)],
    latitude: 30.382029,
    longitude: -9.476421,
    imagePath: 'assets/crocoparc.jpg',
  ),
  AgadirLocation(
    name: ModelLang.golfDuSoleil(),
    description: ModelLang.golfDuSoleilDesc(),
    icon: Icons.golf_course_rounded,
    gradient: [Color(0xFF67E8F9), Color(0xFF06B6D4)],
    latitude: 30.361200,
    longitude: -9.555807,
    imagePath: 'assets/Golf-du-Soleil-Agadir.jpg',
  ),
  AgadirLocation(
    name: ModelLang.amazighMuseumName(),
    description: ModelLang.amazighMuseumDesc(),
    icon: Icons.museum_rounded,
    gradient: [Color(0xFFE879F9), Color(0xFFD946EF)],
    latitude: 30.415983,
    longitude: -9.596979,
    imagePath: 'assets/musee.jpg',
  ),
];
