import 'dart:convert';
import 'package:flutter/cupertino.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:marafik_app/Models/check_connection.dart';
import 'package:marafik_app/Models/data_manager.dart';
import 'package:marafik_app/Models/data_manager.dart';
import 'package:marafik_app/Models/lang.dart';
import 'package:marafik_app/Styles/app_style.dart';
import 'package:marafik_app/config/config_urls.dart';

class NewsPage extends StatefulWidget {
  const NewsPage({Key? key}) : super(key: key);
  @override
  State<NewsPage> createState() => _NewsPageState();
}

class _NewsPageState extends State<NewsPage> {
  List<String>? imageUrl;
  List<String>? title;
  List<String>? date;
  List<String>? body;
  String? author;
  List<dynamic> jsonData = [];
  var jsonImage = [];

  bool isLoading = false;
  bool isLoadingImage = false;
  fillNews() async {
    if (await CheckConnection.isConnected() && jsonData.length == 0) {
      setState(() {
        isLoading = true;
      });
      try {
        var response = await http.get(Uri.parse(ConfigUrls.getNews));
        jsonData = jsonDecode(response.body) as List;
        isLoading = false;
        setState(() {});
        getImageByIdN();
      } catch (e) {}
    }
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    fillNews();
  }

  getImageByIdN() async {
    if (await CheckConnection.isConnected() && jsonData.length != 0) {
      setState(() {
        isLoadingImage = true;
      });
      try {
        jsonImage = [];
        var list = [];
        var response;
        for (var i = 0; i < jsonData.length; i++) {
          response = await http.get(
            Uri.parse(ConfigUrls.getImageByNews + jsonData[i]["id"]),
          );
          list.add(jsonDecode(response.body)["imageHead"]);
        }
        setState(() {
          jsonImage = list;
          isLoadingImage = false;
          DataManager.image = list;
        });
        setS!.setState(() {});
      } catch (e) {}
    }
  }

  @override
  Widget build(BuildContext context) {
    bool ar = DataManager.isArabic;
    return Scaffold(
      body: Container(
        width: MediaQuery.of(context).size.width,
        height: MediaQuery.of(context).size.height,
        color: Colors.grey[200],
        child:
            isLoading == false && jsonData.length != 0
                ? ListView.builder(
                  shrinkWrap: true,
                  padding: EdgeInsets.only(top: 10.h, left: 5.w, right: 5.w),
                  physics: ClampingScrollPhysics(),
                  // itemCount: news.length,
                  itemCount: jsonData.length,
                  itemBuilder: (context, index) {
                    return Column(
                      children: [
                        (ar)
                            ? rowArabic(
                              jsonImage.length == 0
                                  ? ""
                                  : jsonImage[index].toString(),
                              jsonData[index]["titleAr"],
                              jsonData[index]["published_at"],
                              jsonData[index]["descriptionAr"],
                              jsonData[0]["author"],
                              index.toString(),
                            )
                            : row(
                              jsonImage.length == 0
                                  ? ""
                                  : jsonImage[index].toString(),
                              jsonData[index]["titleFr"],
                              jsonData[index]["published_at"],
                              jsonData[index]["descriptionFr"],
                              jsonData[0]["author"],
                              index.toString(),
                            ),
                        index == jsonData.length - 1
                            ? SizedBox(height: 50.h)
                            : Text(""),
                      ],
                    );
                  },
                )
                : isLoading == true && jsonData.length == 0
                ? Container(
                  height: 50.w,
                  width: 50.w,
                  child: Center(
                    child: CircularProgressIndicator(color: Colors.black),
                  ),
                )
                : Container(
                  child: Center(
                    child: Text(
                      ModelLang.noNews(),
                      style: AppStayles.textStyles(Colors.black, 20.sp, true),
                    ),
                  ),
                ),
      ),
    );
  }

  //
  String plus(String text) {
    if (text.length > 120) text = text.substring(0, 120);
    return text;
  }

  Widget row(
    String url,
    String title,
    String date,
    String bodyText,
    String ecritpar,
    String taghero,
  ) {
    double width = 190.w;
    return Column(
      children: [
        // SizedBox(
        //   height: 20.h,
        // ),
        Container(
          height: 150.h,
          width: 360.w,
          decoration: BoxDecoration(
            color: Colors.white,
            // color: Colors.amber,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                spreadRadius: 1,
                blurRadius: 1,
                offset: Offset(0, 3), // changes position of shadow
              ),
            ],
          ),
          child: InkWell(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  // margin: EdgeInsets.symmetric(horizontal: 10.0),
                  width: 158.w,
                  height: 142.h,
                  // color: Colors.red,
                  child: Hero(
                    tag: taghero,
                    child:
                        jsonImage.length != 0
                            ? Container(
                              decoration: BoxDecoration(
                                image: DecorationImage(
                                  fit: BoxFit.fill,
                                  image: MemoryImage(
                                    base64Decode(jsonImage[int.parse(taghero)]),
                                  ),
                                ),
                              ),
                            )
                            : Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                  height: 50.w,
                                  width: 50.w,
                                  child: CircularProgressIndicator(
                                    color: Colors.orange,
                                  ),
                                ),
                              ],
                            ),
                  ),
                ),
                Container(
                  width: 180.w,
                  // color: Colors.red,
                  margin: EdgeInsets.only(left: 10),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // SizedBox(
                      //   height: 0.h,
                      // ),
                      Container(
                        height: 35.w,
                        width: width,
                        // color: Colors.amber,
                        child: Text(
                          title,
                          overflow: TextOverflow.ellipsis,
                          maxLines: 2,
                          textAlign: TextAlign.start,
                          style: TextStyle(
                            fontSize: 13.sp,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      SizedBox(height: 5.w),
                      Row(
                        children: [
                          Container(
                            // color: Colors.red,
                            height: 85.h,
                            width: 180.w,
                            // child: Text(plus(text) + " Plus ...")
                            child: RichText(
                              textAlign: TextAlign.start,
                              text: TextSpan(
                                text: plus(bodyText),
                                style: TextStyle(
                                  fontSize: 13.sp,
                                  color: Colors.grey,
                                  fontWeight: FontWeight.bold,
                                ),
                                children: <TextSpan>[
                                  TextSpan(
                                    text: " plus ...",
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      fontWeight: FontWeight.bold,
                                      color: Color(0xff717D7E),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder:
                      (context) => HeroPage(
                        url: url,
                        title: title,
                        date: date,
                        text: bodyText,
                        // ecritpar: ecritpar,
                        taghero: taghero,
                      ),
                ),
              );
            },
          ),
        ),
        SizedBox(height: 5.h),
      ],
    );
  }

  Widget rowArabic(
    String url,
    String title,
    String date,
    String bodyText,
    String ecritpar,
    String taghero,
  ) {
    double width = 190.w;
    return Column(
      children: [
        // SizedBox(
        //   height: 20.h,
        // ),
        Container(
          height: 150.h,
          width: 360.w,
          decoration: BoxDecoration(
            color: Colors.white,
            // color: Colors.amber,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                spreadRadius: 1,
                blurRadius: 1,
                offset: Offset(0, 3), // changes position of shadow
              ),
            ],
          ),
          child: InkWell(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  width: 180.w,
                  // color: Colors.red,
                  margin: EdgeInsets.only(left: 10),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // SizedBox(
                      //   height: 0.h,
                      // ),
                      Container(
                        // height: 55.h,
                        height: 30.w,
                        width: width,
                        // color: Colors.amber,
                        child: Directionality(
                          textDirection: TextDirection.rtl,
                          child: Text(
                            title,
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                            textAlign: TextAlign.start,
                            style: TextStyle(
                              fontSize: 15.sp,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      // SizedBox(
                      //   height: 5.h,
                      // ),
                      Row(
                        children: [
                          Container(
                            // color: Colors.red,
                            height: 85.h,
                            width: 180.w,
                            // child: Text(plus(text) + " Plus ...")
                            child: RichText(
                              textDirection: TextDirection.rtl,
                              textAlign: TextAlign.start,
                              text: TextSpan(
                                text: plus(bodyText),
                                style: TextStyle(
                                  fontSize: 13.sp,
                                  color: Colors.grey,
                                  fontWeight: FontWeight.bold,
                                ),
                                children: <TextSpan>[
                                  TextSpan(
                                    text: " المزيد ...",
                                    style: TextStyle(
                                      fontSize: 12.sp,
                                      fontWeight: FontWeight.bold,
                                      color: Color(0xff717D7E),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                SizedBox(width: 5.h),
                Container(
                  // margin: EdgeInsets.symmetric(horizontal: 10.0),
                  width: 155.w,
                  height: 142.h,
                  // color: Colors.red,
                  child: Hero(
                    tag: taghero,
                    child:
                        jsonImage.length != 0
                            ? Container(
                              decoration: BoxDecoration(
                                image: DecorationImage(
                                  fit: BoxFit.fill,
                                  image: MemoryImage(
                                    base64Decode(jsonImage[int.parse(taghero)]),
                                  ),
                                ),
                              ),
                            )
                            : Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Container(
                                  height: 50.w,
                                  width: 50.w,
                                  child: CircularProgressIndicator(
                                    color: Colors.orange,
                                  ),
                                ),
                              ],
                            ),
                  ),
                ),
              ],
            ),
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder:
                      (context) => HeroPage(
                        url: url,
                        title: title,
                        date: date,
                        text: bodyText,
                        // ecritpar: ecritpar,
                        taghero: taghero,
                      ),
                ),
              );
            },
          ),
        ),
        SizedBox(height: 5.h),
      ],
    );
  }
}

class HeroPage extends StatefulWidget {
  final String? url;
  final String? title;
  final String? date;
  final String? text;
  // final String ecritpar;
  final String? taghero;
  const HeroPage({
    Key? key,
    this.url,
    this.title,
    this.date,
    this.text,
    // required this.ecritpar,
    this.taghero,
  }) : super(key: key);
  @override
  _HeroPageState createState() => _HeroPageState();
}

_HeroPageState? setS;

class _HeroPageState extends State<HeroPage> {
  @override
  Widget build(BuildContext context) {
    setS = this;
    double width = MediaQuery.of(context).size.width;
    String url = widget.url!;
    String title = widget.title!;
    String date = widget.date!;
    String text = widget.text!;
    // String ecritpar = widget.ecritpar;
    String taghero = widget.taghero!;
    bool ar = DataManager.isArabic;
    return SafeArea(
      child: Scaffold(
        body: SingleChildScrollView(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: width,
                // color: Colors.blue,
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // SizedBox(
                    //   height: 20.h,
                    // ),
                    Hero(
                      tag: taghero,
                      child: Container(
                        height: 200.h,
                        width: 500.w,
                        decoration: BoxDecoration(
                          // border: Border.all(),
                          // color: Colors.amber,
                          image:
                              DataManager.image.length == 0
                                  ? DecorationImage(
                                    fit: BoxFit.fill,
                                    image: AssetImage("assets/empty.png"),
                                  )
                                  : DecorationImage(
                                    fit: BoxFit.fill,
                                    image: MemoryImage(
                                      base64Decode(
                                        DataManager.image[int.parse(taghero)],
                                      ),
                                    ),
                                  ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Row(
                              mainAxisAlignment:
                                  (ar)
                                      ? MainAxisAlignment.end
                                      : MainAxisAlignment.start,
                              children: [
                                Container(
                                  height: 26.h,
                                  width: 145.h,
                                  color: Colors.white70,
                                  child: Container(
                                    width: width - 20.h,
                                    // color: Colors.amber,
                                    child:
                                        (ar)
                                            ? Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Directionality(
                                                  textDirection:
                                                      TextDirection.rtl,
                                                  child: Row(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment
                                                            .center,
                                                    mainAxisAlignment:
                                                        MainAxisAlignment
                                                            .center,
                                                    children: [
                                                      Text(
                                                        " بتاريخ ",
                                                        // textAlign: TextAlign.right,
                                                        style: TextStyle(
                                                          color: Color(
                                                            0xff717D7E,
                                                          ),
                                                          fontSize: 12.sp,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                        ),
                                                        textAlign:
                                                            TextAlign.center,
                                                      ),
                                                      Text(
                                                        date.substring(0, 10),
                                                        // textAlign: TextAlign.right,
                                                        style: TextStyle(
                                                          color: Color(
                                                            0xff717D7E,
                                                          ),
                                                          fontSize: 12.sp,
                                                          fontWeight:
                                                              FontWeight.bold,
                                                        ),
                                                        textAlign:
                                                            TextAlign.center,
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ],
                                            )
                                            : Column(
                                              mainAxisAlignment:
                                                  MainAxisAlignment.center,
                                              children: [
                                                Text(
                                                  "Le " + date.substring(0, 10),
                                                  // textAlign: TextAlign.right,
                                                  style: TextStyle(
                                                    color: Color(0xff717D7E),
                                                    fontSize: 15.sp,
                                                    fontWeight: FontWeight.bold,
                                                  ),
                                                ),
                                              ],
                                            ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: 10.h),
                    Container(
                      // color: Colors.red,
                      width: width - 5.w,
                      child:
                          (ar)
                              ? Directionality(
                                textDirection: TextDirection.rtl,
                                child: Text(
                                  title,
                                  style: TextStyle(
                                    fontSize: 24.sp,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  textAlign: TextAlign.start,
                                ),
                              )
                              : Text(
                                title,
                                style: TextStyle(
                                  fontSize: 24.sp,
                                  fontWeight: FontWeight.bold,
                                ),
                                textAlign: TextAlign.start,
                              ),
                    ),
                    SizedBox(height: 10.h),
                    SizedBox(height: 0.h),
                    Container(
                      width: width,
                      //  color: Colors.blue,
                      margin: EdgeInsets.all(5.w),
                      child:
                          (ar)
                              ? Directionality(
                                textDirection: TextDirection.rtl,
                                child: Text(
                                  text,
                                  style: TextStyle(
                                    color: Colors.black,
                                    fontSize: 16.sp,
                                  ),
                                  textAlign: TextAlign.start,
                                ),
                              )
                              : Text(
                                text,
                                style: TextStyle(
                                  color: Colors.black,
                                  fontSize: 16.sp,
                                ),
                                textAlign: TextAlign.start,
                              ),
                    ),
                    SizedBox(height: 5.h),
                    // Container(
                    //     width: width - 35.h,
                    //     child: (ar)
                    //         ? Text(
                    //             ecritpar + " " + DateTime.now().year.toString(),
                    //             style: TextStyle(
                    //                 color:Color(0xffBD9595) ,
                    //                 fontSize: 12.sp,
                    //                 fontWeight: FontWeight.w600),
                    //             textAlign: TextAlign.end,
                    //           )
                    //         : Directionality(
                    //             textDirection: TextDirection.rtl,
                    //             child: Text(
                    //               DateTime.now().year.toString() +
                    //                   " " +
                    //                   ecritpar,
                    //               style: TextStyle(
                    //                   color:Color(0xffBD9595) ,
                    //                   fontSize: 12.sp,
                    //                   fontWeight: FontWeight.w600),
                    //               textAlign: TextAlign.end,
                    //             ),
                    //           )),
                    SizedBox(height: 40.h),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
