import 'dart:convert';
import 'package:hexcolor/hexcolor.dart';
import 'package:http/http.dart' as http;
import 'package:flutter/material.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:marafik_app/Models/check_connection.dart';
import 'package:marafik_app/Models/data_manager.dart';
import 'package:marafik_app/Models/lang.dart';
import 'package:marafik_app/Pages/Bus/bus_choix.dart';
import 'package:marafik_app/Pages/gps_page.dart';
import 'package:marafik_app/Pages/qr_page.dart';
import 'package:marafik_app/Pages/widgets/secotr_card.dart';
import 'package:marafik_app/Pages/wms/wms_page.dart';
import 'package:marafik_app/Styles/app_style.dart';
import 'package:marafik_app/config/config_urls.dart';
import 'package:marafik_app/database/complaint.dart';
import 'package:marafik_app/database/sector.dart';
import 'package:marafik_app/database/sqlite.dart';
import 'package:marafik_app/jira/identifications.dart';
import 'package:shared_preferences/shared_preferences.dart';

class SecondPage extends StatefulWidget {
  const SecondPage({Key? key}) : super(key: key);

  @override
  _SecondPageState createState() => _SecondPageState();
}

class _SecondPageState extends State<SecondPage> {
  @override
  void initState() {
    // TODO: implement initState
    fillSector();
    fillData();
    fillMyRequest();
    super.initState();
  }

  DbHelper db = DbHelper();
  fillMyRequest() async {
    DataManager.listRequest = await db.getAllComplaints(DataManager.isArabic);
  }

  List myList = [
    ModelLang.pcp(),
    ModelLang.wms(),
    ModelLang.transport(),
    ModelLang.espace(),
    ModelLang.batoire(),
    ModelLang.eauAssain(),
    ModelLang.voirie(),
    ModelLang.securite(),
  ];

  List myListImage = [
    "assets/pcp-icon.png",
    "assets/wms-icon.png",
    "assets/bus-icon.png",
    "assets/espace-icon.png",
    "assets/bat-icon.png",
    "assets/water_pipe.png",
    "assets/road.png",
    "assets/security.png",
  ];

  List sousList = [
    ModelLang.pcpMessage(),
    ModelLang.wmsMessage(),
    ModelLang.busMessage(),
    ModelLang.escapevMessage(),
    ModelLang.batoireMessage(),
    ModelLang.eauAssainMessage(),
    ModelLang.voirieMessage(),
    ModelLang.securiteMessage(),
  ];

  List myListPage = [
    QrCodePage(),
    WmsChoose(),
    BusChoose(),
    GpsPage(),
    QrCodePage(),
  ];

  Future<void> fillSector() async {
    DbHelper db = DbHelper();
    List<Sector> sectors = await db.getAllSector();
    if (sectors.isEmpty) {
      await db.insertSector(Sector.creat(1, "PCP", "أسواق القرب"));
      await db.insertSector(Sector.creat(2, "Déchets", "قمامة"));
      await db.insertSector(Sector.creat(3, "Transport", "الحافلة"));
      await db.insertSector(Sector.creat(4, "Espace vert", "مساحات الخضراء"));
      await db.insertSector(Sector.creat(5, "Abattoir", "المجزرة"));
      await db.insertSector(Sector.creat(6, "Voirie", "الطرقات"));
      await db.insertSector(
        Sector.creat(7, "Eau et Assainissement", "الماء والتطهير"),
      );
      await db.insertSector(Sector.creat(8, "Sécurité", "الأمن"));
    }
  }

  Future<void> fillData() async {
    DbHelper db = DbHelper();
    apiRequest() async {
      await db.deleteAllInfoAswaq();
      await db.deleteAllInfoTrash();
      await db.deleteAllInfoTrans();
      // var response = await http.get(Uri.parse(ConfigUrls.apiPcpShop));
      var jsonString = '[]';
      List<dynamic> jsonData = jsonDecode(jsonString) as List;
      String sql = "";
      String query = "insert into ${DbHelper.tableInfoAswaq} values";
      String rqt = "";

      for (var i = 0; i < jsonData.length; i++) {
        if (rqt != "") {
          rqt += ",";
        }
        rqt +=
            "(NULL,'${jsonData[i]["QrStore"]}','${jsonData[i]["DmStore"]}','${jsonData[i]["QrMerchant"]}')";
      }

      sql = query + rqt + ";";
      await db.executeQuery(sql);
      // response = await http.get(Uri.parse(ConfigUrls.apiQrBacs));

      jsonData = jsonDecode(jsonString) as List;
      query = "insert into ${DbHelper.tableInfoTrash} values";
      rqt = "";
      for (var i = 0; i < jsonData.length; i++) {
        if (rqt != "") rqt += ",";
        rqt += "(NULL,'${jsonData[i]["token"]}')";
      }
      sql = query + rqt + ";";
      await db.executeQuery(sql);
      // response = await http.get(Uri.parse(ConfigUrls.apiTransport));
      jsonData = jsonDecode(jsonString) as List;
      query = "insert into ${DbHelper.tableInfoTrans} values";
      rqt = "";
      for (var i = 0; i < jsonData.length; i++) {
        if (rqt != "") rqt += ",";
        rqt += "(NULL,'${jsonData[i]["code"]}','')";
      }
      sql = query + rqt + ";";
      await db.executeQuery(sql);

      // response = await http.get(Uri.parse(dotenv.env['apiBatoire'].toString()));
      // jsonData = jsonDecode(response.body) as List;
      // await db.deleteAllInfoBatoire();
      // query = "insert into infoBatoire values";
      // rqt = "";
      // for (var i = 0; i < jsonData.length; i++) {
      //   if (rqt != "") rqt = ",";
      //   rqt = "(NULL,'${jsonData[i]["qr"]}')";
      // }
      // sql = query + rqt + ";";
      // await db.executeQuery(sql);
      //
    }

    if (await CheckConnection.isConnected()) {
      if (!(await db.isDatabaseFull())) {
        apiRequest();
        DataManager.preferences = await SharedPreferences.getInstance();

        DataManager.preferences!.setString(
          "lastSyn",
          (DateTime.now().day).toString(),
        );
      } else {
        DataManager.preferences = await SharedPreferences.getInstance();
        if (int.parse(DataManager.preferences!.getString("lastSyn")!) !=
            DateTime.now().day) {
          DataManager.preferences = await SharedPreferences.getInstance();

          apiRequest();
          DataManager.preferences!.setString(
            "lastSyn",
            (DateTime.now().day).toString(),
          );
        }
      }

      List<Complaint> complaints = await db.getAllComplaintsNotProccessed();
      var head = {
        'Content-Type': 'application/json',
        'Authorization':
            'Basic ' +
            base64Encode(
              utf8.encode(ConfigUrls.emailJira + ":" + ConfigUrls.password),
            ),
      };

      for (var i = 0; i < complaints.length; i++) {
        var responseJira = await http.get(
          Uri.parse(complaints[i].idJira),
          headers: head,
        );
        // var jsonDataJira = jsonDecode(responseJira.body)
        // as List
        // ;

        if (responseJira.statusCode == 200) {
          print("----------$i---------");
          if (jsonDecode(responseJira.body)["fields"]["status"]["name"] ==
              "Work in progress") {
            await db.updateComplaintState(
              complaints[i].id,
              ComplaintState.en_traitement,
            );
          } else if (jsonDecode(
                responseJira.body,
              )["fields"]["status"]["name"] ==
              "Done") {
            await db.updateComplaintState(
              complaints[i].id,
              ComplaintState.traite,
            );
          }
        }
      }
    }
  }

  // void loadPictures() {
  //   precacheImage(AssetImage("assets/pcp-icon.png"), context);
  //   precacheImage(AssetImage("assets/wms-icon.png"), context);
  //   precacheImage(AssetImage("assets/bus-icon.png"), context);
  //   precacheImage(AssetImage("assets/espace-icon.png"), context);
  //   precacheImage(AssetImage("assets/bat-icon.png"), context);
  // }

  @override
  Widget build(BuildContext context) {
    return Align(
      // height: size.height,
      // width: size.width,
      child: Stack(
        children: [
          Align(
            alignment: Alignment.topCenter,
            child: ListView.builder(
              shrinkWrap: true,
              // padding: EdgeInsets.only(left: 10.w),
              physics: const ClampingScrollPhysics(),
              itemCount: 1,
              itemBuilder: (context, index) {
                return Column(
                  children: [
                    index == 0
                        ? Container(
                          height: 180.w,
                          // width: MediaQuery.of(context).size.width,
                          decoration: const BoxDecoration(
                            // color: Colors.amber,
                            image: DecorationImage(
                              image: AssetImage("assets/agadir.jpeg"),
                              fit: BoxFit.fitWidth,
                            ),
                          ),
                        )
                        : Container(),
                    Container(
                      color: Colors.grey[200],
                      width: MediaQuery.of(context).size.width,
                      // height: MediaQuery.of(context).size.height,
                      child: Container(
                        // color: Colors.red,
                        child: Column(
                          children: [
                            Container(
                              margin: EdgeInsets.symmetric(horizontal: 10.w),
                              // color: Colors.amber,
                              width: MediaQuery.of(context).size.width,
                              height: 50.h,
                              child: Center(
                                child: Text(
                                  ModelLang.reportProblem(),
                                  style: AppStayles.textStyles(
                                    Colors.grey,
                                    18.sp,
                                    false,
                                  ),
                                ),
                              ),
                            ),
                            GridView.builder(
                              gridDelegate:
                                  SliverGridDelegateWithFixedCrossAxisCount(
                                    crossAxisCount: _getCrossAxisCount(context),
                                    childAspectRatio: _getChildAspectRatio(
                                      context,
                                    ),
                                    crossAxisSpacing: 8.w,
                                    mainAxisSpacing: 8.h,
                                  ),
                              controller: ScrollController(
                                keepScrollOffset: false,
                              ),
                              shrinkWrap: true,
                              scrollDirection: Axis.vertical,
                              padding: EdgeInsets.symmetric(
                                horizontal: 12.w,
                                vertical: 8.h,
                              ),
                              itemCount: myList.length,
                              itemBuilder: (BuildContext ctx, index) {
                                return SectorCard(
                                  sectorIndex: index,
                                  title: myList[index],
                                  description: sousList[index],
                                  imagePath: myListImage[index],
                                  onTap: () {
                                    DataManager.idSector = index + 1;
                                    DataManager.nomSector = myList[index];
                                    print(
                                      "Selected sector ID: ${DataManager.idSector}",
                                    );

                                    AppStayles.myReportDialog(
                                      context,
                                      myList[index],
                                      ModelLang.allMessage(),
                                      myListImage[index],
                                      myListPage[index],
                                    );
                                  },
                                );
                              },
                            ),
                            SizedBox(height: 10.h),
                          ],
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  int _getCrossAxisCount(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1200) return 4; // Desktop/Large tablets
    if (screenWidth > 800) return 3; // Medium tablets
    if (screenWidth > 600) return 2; // Small tablets/Large phones
    return 2; // Mobile phones
  }

  double _getChildAspectRatio(BuildContext context) {
    double screenWidth = MediaQuery.of(context).size.width;
    if (screenWidth > 1200) return 0.85; // Desktop
    if (screenWidth > 800) return 0.8; // Medium tablets
    if (screenWidth > 600) return 0.75; // Small tablets
    return 0.7; // Mobile phones - More height for content
  }
}
