
// import 'package:flutter/material.dart';
// import 'package:flutter/services.dart';
// import 'package:sdmb_controllers/Models/data_manager.dart';
// import 'package:sdmb_controllers/Pages/login_page.dart';
// import 'package:sdmb_controllers/Pages/second_page.dart';
// import 'package:sdmb_controllers/Styles/app_style.dart';
// import 'package:shared_preferences/shared_preferences.dart';
// import 'package:video_player/video_player.dart';
// import 'package:sdmb_controllers/Pages/video_page.dart';
// class SplashScreen extends StatefulWidget {
//   const SplashScreen({Key? key}) : super(key: key);
//   @override
//   _SplashScreenState createState() => _SplashScreenState();
// }
// class _SplashScreenState extends State<SplashScreen> {
//   VideoPlayerController? _controller;
//   bool _visible = false;
//   bool isLoided = false;
//   @override
//   void initState() {
//     super.initState();
//     SystemChrome.setPreferredOrientations([
//       DeviceOrientation.portraitUp,
//     ]);
//     init();
//   }
//   Future init() async {
//     _controller = await VideoPlayerController.asset("assets/splash.m4v")
//       ..addListener(() {})
//       ..setLooping(false)
//       ..initialize();
//     isLoided = true;
//     await _controller!.play();
//     setState(() {});
//     Future.delayed(
//       Duration(milliseconds: 2000),
//       () async {
//         DataManager.preferences = await SharedPreferences.getInstance();
//         var user = DataManager.preferences!.getString("user");
//         if (_controller != null) {
//           _controller!.dispose();
//           _controller = null;
//         }
//         if (user != null) {
//           Navigator.pushAndRemoveUntil(
//             context,
//             MaterialPageRoute(
//               builder: (BuildContext context) => SecondPage(),
//             ),
//             (route) => false,
//           );
//         } else {
//           Navigator.pushAndRemoveUntil(
//             context,
//             MaterialPageRoute(
//               builder: (BuildContext context) => LoginPage(),
//             ),
//             (route) => false,
//           );
//         }
//       },
//     );
//   }
//   @override
//   void dispose() {
//     super.dispose();
//     if (_controller != null) {
//       _controller!.dispose();
//       _controller = null;
//     }
//   }
//   _getVideoBackground() {
//     return VideoPlayer(_controller!);
//   }
//   void loadPictures() {
//     precacheImage(AssetImage("assets/back_sdmb.png"), context);
//     precacheImage(AssetImage("assets/app-bar.png"), context);
//   }
//   Future<bool> onWillPop() async {
//     return false;
//   }
//   @override
//   Widget build(BuildContext context) {
//     loadPictures();
//     return SafeArea(
//       child: WillPopScope(
//         onWillPop: onWillPop,
//         child: Scaffold(
//           body: Stack(
//             children: [
//               if (isLoided)
//                 SingleChildScrollView(
//                   physics: NeverScrollableScrollPhysics(),
//                   child: Container(
//                     width: MediaQuery.of(context).size.width,
//                     height: MediaQuery.of(context).size.width * 2.333,
//                     child: VideoPlayer(_controller!),
//                   ),
//                 ),
//             ],
//           ),
//         ),
//       ),
//     );
//   }
// }