import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:marafik_app/Models/lang.dart';

class TourismPage extends StatelessWidget {
  const TourismPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Hero section
            Container(
              width: double.infinity,
              height: 200.h,
              decoration: BoxDecoration(
                image: DecorationImage(
                  image: AssetImage('assets/agadir.jpeg'),
                  fit: BoxFit.cover,
                ),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [Color(0xFF8B5CF6), Color(0xFF6366F1)],
                ),
                borderRadius: BorderRadius.circular(16.r),
              ),
              child: Padding(
                padding: EdgeInsets.all(20.w),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      ModelLang.discoverAgadir(),
                      style: TextStyle(
                        fontSize: 28.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(height: 8.h),
                    Text(
                      ModelLang.pearlOfSouth(),
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: Colors.white.withOpacity(0.9),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 24.h),

            // Tourism categories
            ...tourismCategories
                .map(
                  (category) => Container(
                    margin: EdgeInsets.only(bottom: 16.h),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16.r),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: EdgeInsets.all(16.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Container(
                                padding: EdgeInsets.all(12.w),
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    colors: category.gradient,
                                  ),
                                  borderRadius: BorderRadius.circular(12.r),
                                ),
                                child: Icon(
                                  category.icon,
                                  color: Colors.white,
                                  size: 24.sp,
                                ),
                              ),
                              SizedBox(width: 16.w),
                              Expanded(
                                child: Text(
                                  category.title,
                                  style: TextStyle(
                                    fontSize: 18.sp,
                                    fontWeight: FontWeight.w600,
                                    color: Colors.black87,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 12.h),
                          Text(
                            category.description,
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.grey[600],
                              height: 1.4,
                            ),
                          ),
                          if (category.highlights.isNotEmpty) ...[
                            SizedBox(height: 12.h),
                            ...category.highlights
                                .map(
                                  (highlight) => Padding(
                                    padding: EdgeInsets.only(bottom: 4.h),
                                    child: Row(
                                      children: [
                                        Icon(
                                          Icons.star_rounded,
                                          size: 16.sp,
                                          color: Colors.amber,
                                        ),
                                        SizedBox(width: 8.w),
                                        Expanded(
                                          child: Text(
                                            highlight,
                                            style: TextStyle(
                                              fontSize: 12.sp,
                                              color: Colors.grey[700],
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                )
                                .toList(),
                          ],
                        ],
                      ),
                    ),
                  ),
                )
                .toList(),
          ],
        ),
      ),
    );
  }
}

// Static data - Updated to use ModelLang
final List<TourismCategory> tourismCategories = [
  TourismCategory(
    title: ModelLang.beachesAndWaterSports(),
    description: ModelLang.beachesDescription(),
    icon: Icons.surfing_rounded,
    gradient: [Color(0xFF60A5FA), Color(0xFF3B82F6)],
    highlights: [
      ModelLang.surfAndKitesurf(),
      ModelLang.sandyBeach(),
      ModelLang.waterSportsSchools(),
    ],
  ),
  TourismCategory(
    title: ModelLang.cultureAndHistory(),
    description: ModelLang.cultureDescription(),
    icon: Icons.museum_rounded,
    gradient: [Color(0xFFA78BFA), Color(0xFF8B5CF6)],
    highlights: [
      ModelLang.kasbaAgadirOufella(),
      ModelLang.amazighMuseum(),
      ModelLang.modernArchitecture(),
    ],
  ),
  TourismCategory(
    title: ModelLang.gastronomy(),
    description: ModelLang.gastronomyDescription(),
    icon: Icons.restaurant_rounded,
    gradient: [Color(0xFFFBBF24), Color(0xFFF59E0B)],
    highlights: [
      ModelLang.freshFish(),
      ModelLang.traditionalTajines(),
      ModelLang.moroccanPastries(),
    ],
  ),
  TourismCategory(
    title: ModelLang.shoppingAndCrafts(),
    description: ModelLang.shoppingDescription(),
    icon: Icons.shopping_cart_rounded,
    gradient: [Color(0xFF34D399), Color(0xFF10B981)],
    highlights: [
      ModelLang.soukElHad(),
      ModelLang.berberCrafts(),
      ModelLang.modernMalls(),
    ],
  ),
];

class TourismCategory {
  final String title;
  final String description;
  final IconData icon;
  final List<Color> gradient;
  final List<String> highlights;

  TourismCategory({
    required this.title,
    required this.description,
    required this.icon,
    required this.gradient,
    required this.highlights,
  });
}
