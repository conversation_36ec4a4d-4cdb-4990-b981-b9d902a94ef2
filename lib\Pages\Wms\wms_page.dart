import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:marafik_app/Models/data_manager.dart';
import 'package:marafik_app/Models/display_module.dart';
import 'package:marafik_app/Models/lang.dart';
import 'package:marafik_app/Models/navbar.dart';
import 'package:marafik_app/Pages/gps_page.dart';
import 'package:marafik_app/Pages/qr_page.dart';
import 'package:marafik_app/Styles/app_style.dart';

class WmsChoose extends StatefulWidget {
  @override
  _WmsChooseState createState() => _WmsChooseState();
}

class _WmsChooseState extends State<WmsChoose> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    DataManager.canGo = false;
    DataManager.previouspage = NavBar();
    DataManager.footerText = ModelLang.wmsMessage();
    Future.delayed(Duration(milliseconds: 500), () {
      DataManager.updateProgress(0);
    });
  }

  Future<bool> _onWillPop() async {
    AppStayles.navigateTo(context, NavBar());
    return false;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: _onWillPop,
      child: DisplayModule(
        Center(
          child: Container(
            // color: Colors.amber,
            height: 500.h,
            child: Column(
              children: [
                SizedBox(height: 250.h),
                myButton(Icons.delete_outlined, ModelLang.bac(), 1, context),
                SizedBox(width: 1.w, height: 12.h),
                myButton(
                  Icons.cleaning_services_outlined,
                  ModelLang.netoyment(),
                  2,
                  context,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

Widget myButton(IconData icon, String txt, int ontap, BuildContext context) {
  return Container(
    width: DataManager.isArabic ? 170.w : 200.w,
    height: 90.h,
    decoration: BoxDecoration(
      gradient: AppStayles.colorGradient(),
      borderRadius: BorderRadius.all(Radius.circular(4.w)),
    ),
    child: InkWell(
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(width: 25.w, height: 1.h),
          Icon(icon, size: 28.w, color: Colors.white),
          SizedBox(width: 5.w, height: 1.h),
          DataManager.isArabic
              ? Text(
                txt,
                style: AppStayles.textStyles(Colors.white, 25.sp, true),
                // TextStyle(fontSize: 34.sp, color: Colors.white),
              )
              : Text(
                txt,
                style: AppStayles.textStyles(Colors.white, 20.sp, true),
                // TextStyle(fontSize: 34.sp, color: Colors.white),
              ),
        ],
      ),
      onTap: () {
        if (ontap == 1) {
          print("hawiyat");
          AppStayles.navigateTo(context, QrCodePage());
        } else {
          print("nadafa");
          AppStayles.navigateTo(context, GpsPage());
        }
      },
    ),
  );
}
