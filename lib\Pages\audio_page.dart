import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:marafik_app/Models/display_module.dart';
import 'package:marafik_app/Models/models_page/audio_handeler.dart';
import 'package:marafik_app/Models/data_manager.dart';
import 'package:marafik_app/Models/lang.dart';
import 'package:marafik_app/Pages/image_page.dart';
import 'package:marafik_app/Pages/succes_page.dart';
import 'package:marafik_app/Styles/app_style.dart';
import 'package:path_provider/path_provider.dart';

class AudioPage extends StatefulWidget {
  const AudioPage({Key? key}) : super(key: key);

  @override
  _AudioPageState createState() => _AudioPageState();
}

class _AudioPageState extends State<AudioPage> {
  late AudioHandeler audioHandeler;
  bool isRecorded = false;
  File? savedAudioFile;

  // Your file path method
  Future<String> getRecordFilePath() async {
    Directory storageDirectory = await getApplicationDocumentsDirectory();
    String sdPath = storageDirectory.path + "/record";
    String fileName = "/tempRecord.mp3";
    var directory = Directory(sdPath);
    if (!directory.existsSync()) directory.createSync(recursive: true);
    File tempFile = File(sdPath + fileName);
    if (tempFile.existsSync()) tempFile.deleteSync();
    return sdPath + fileName;
  }

  // Method to save the audio after recording
  Future<void> saveAudioFile() async {
    try {
      if (audioHandeler.recordFilePath.isNotEmpty) {
        File tempFile = File(audioHandeler.recordFilePath);
        if (tempFile.existsSync()) {
          // Create permanent file path
          Directory storageDirectory = await getApplicationDocumentsDirectory();
          String permanentPath = storageDirectory.path + "/record";
          String timestamp = DateTime.now().millisecondsSinceEpoch.toString();
          String fileName = "/saved_record_$timestamp.mp3";

          var directory = Directory(permanentPath);
          if (!directory.existsSync()) directory.createSync(recursive: true);

          savedAudioFile = File(permanentPath + fileName);

          // Copy temp file to permanent location
          await tempFile.copy(savedAudioFile!.path);

          // Set the saved file to DataManager
          DataManager.audioFile = savedAudioFile;

          print("Audio saved to: ${savedAudioFile!.path}");
        }
      }
    } catch (e) {
      print("Error saving audio: $e");
    }
  }

  @override
  void initState() {
    super.initState();
    audioHandeler = AudioHandeler(
      onUpdate: () {
        setState(() {
          // Update isRecorded based on the current recording state
          isRecorded = audioHandeler.myRecordState == RecordState.recorded;
        });
      },
    );
    DataManager.previouspage = ImagesPage();
    Future.delayed(Duration(milliseconds: 500), () {
      DataManager.updateProgress(60);
    });
  }

  Future? myNextAction() async {
    if (audioHandeler.myRecordState == RecordState.recorded &&
        savedAudioFile != null) {
      // Recording is finished and saved, proceed to next page
      DataManager.audioFile = savedAudioFile;
      AppStayles.navigateTo(context, Succes());
    } else if (audioHandeler.myRecordState == RecordState.none) {
      AppStayles.fluttertoast(ModelLang.rappelAudio(), Colors.red[300]!);
    } else if (savedAudioFile == null &&
        audioHandeler.myRecordState == RecordState.recorded) {
      // Try to save the file if it's not saved yet
      await saveAudioFile();
      if (savedAudioFile != null) {
        DataManager.audioFile = savedAudioFile;
        AppStayles.navigateTo(context, Succes());
      }
    }
    return null;
  }

  void _navigateToNext() async {
    if (audioHandeler.myRecordState == RecordState.recorded &&
        savedAudioFile != null) {
      // Ensure the file is set in DataManager
      DataManager.audioFile = savedAudioFile;
      AppStayles.navigateTo(context, Succes());
    } else if (savedAudioFile == null) {
      // If no saved file, try to save it first
      await saveAudioFile();
      if (savedAudioFile != null) {
        DataManager.audioFile = savedAudioFile;
        AppStayles.navigateTo(context, Succes());
      } else {
        AppStayles.fluttertoast("Error saving audio file", Colors.red[300]!);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    DataManager.footerText = ModelLang.audio();

    double sWidth = MediaQuery.of(context).size.width;
    int seconds = audioHandeler.secondsPassed % 60;
    int minutes = audioHandeler.secondsPassed ~/ 60;

    DataManager.myNextAction = myNextAction;

    Function? prv() {
      AppStayles.navigateTo(context, AudioPage());
      DataManager.canGo = true;
      return null;
    }

    return DisplayModule(
      Stack(
        children: [
          Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Container(
                height: 315.w,
                width: MediaQuery.of(context).size.width,
                child: Stack(
                  children: [
                    Align(
                      alignment: Alignment.topCenter,
                      child: Column(
                        children: [
                          SizedBox(height: 70.w),
                          audioHandeler.recordStateString()!,
                          SizedBox(height: 10.w),
                        ],
                      ),
                    ),
                    Positioned(
                      top: 215.w,
                      width: sWidth,
                      child: Center(
                        child: AnimatedContainer(
                          width:
                              audioHandeler.myRecordState == RecordState.none
                                  ? 0
                                  : 65.w,
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeOutCubic,
                          color: Colors.transparent,
                          child: FittedBox(
                            fit: BoxFit.fitWidth,
                            child: Text(
                              minutes.toString().padLeft(2, '0') +
                                  ":" +
                                  seconds.toString().padLeft(2, '0'),
                              style: TextStyle(fontFamily: 'Montserrat'),
                            ),
                          ),
                        ),
                      ),
                    ),
                    AnimatedPositioned(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeOutCubic,
                      top:
                          audioHandeler.myRecordState == RecordState.none
                              ? 140.w
                              : 250.w,
                      left: 162.w,
                      child: InkWell(
                        overlayColor: MaterialStateProperty.all(
                          Colors.transparent,
                        ),
                        child: Container(
                          decoration: BoxDecoration(
                            boxShadow: const [
                              BoxShadow(
                                color: Color.fromARGB(50, 0, 0, 0),
                                offset: Offset(0, 2.7),
                                spreadRadius: 1,
                                blurRadius: 5,
                              ),
                            ],
                            color: HexColor("#D53434"),
                            borderRadius: BorderRadius.circular(100),
                          ),
                          height: 36.w,
                          width: 36.w,
                          child: Icon(
                            Icons.delete,
                            color: Colors.white,
                            size: 20.w,
                          ),
                        ),
                        onTap: () async {
                          audioHandeler.deleteAudio();
                          setState(() {
                            isRecorded = false;
                            savedAudioFile = null;
                          });
                        },
                      ),
                    ),
                    Center(
                      child: InkWell(
                        overlayColor: MaterialStateProperty.all(
                          Colors.transparent,
                        ),
                        child: AnimatedContainer(
                          duration: const Duration(milliseconds: 300),
                          curve: Curves.easeOutCubic,
                          decoration: BoxDecoration(
                            boxShadow: const [
                              BoxShadow(
                                color: Color.fromARGB(50, 0, 0, 0),
                                offset: Offset(0, 2.7),
                                spreadRadius: 1,
                                blurRadius: 5,
                              ),
                            ],
                            gradient: AppStayles.colorGradient(),
                            borderRadius: BorderRadius.circular(
                              audioHandeler.myRecordState == RecordState.none ||
                                      audioHandeler.myRecordState ==
                                          RecordState.recorded
                                  ? 200.w
                                  : 4.w,
                            ),
                            image: DecorationImage(
                              image: AssetImage("assets/micLogo.png"),
                              scale: 3.5,
                            ),
                          ),
                          height: 108.w,
                          width: 104.4.w,
                        ),
                        onTap: () {
                          audioHandeler.startStopRecord();
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          // Next button positioned at the bottom
          if (isRecorded)
            Positioned(
              bottom: 20.w,
              left: 0,
              right: 0,
              child: Center(
                child: InkWell(
                  onTap: _navigateToNext,
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5.w),
                      color: AppStayles.fluttertoastBackgroundHexa.withAlpha(
                        200,
                      ),
                    ),
                    width: 90.w,
                    height: 35.w,
                    child: Center(
                      child: Text(
                        ModelLang.next(),
                        style: AppStayles.textStyles(Colors.white, 18.sp, true),
                      ),
                    ),
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }
}
