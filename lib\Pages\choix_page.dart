import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:marafik_app/Models/data_manager.dart';
import 'package:marafik_app/Models/display_module.dart';
import 'package:marafik_app/Models/lang.dart';
import 'package:marafik_app/Models/navbar.dart';
import 'package:marafik_app/Pages/image_page.dart';
import 'package:marafik_app/Pages/qr_page.dart';
import 'package:marafik_app/Pages/video_page.dart';
import 'package:marafik_app/Pages/wms/wms_page.dart';
import 'package:marafik_app/Styles/app_style.dart';

class ChoixPage extends StatefulWidget {
  const ChoixPage({Key? key}) : super(key: key);

  @override
  _ChoixPageState createState() => _ChoixPageState();
}

class _ChoixPageState extends State<ChoixPage> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    DataManager.previouspage = previouspage();
  }

  @override
  Widget build(BuildContext context) {
    DataManager.footerText = ModelLang.pickImgOrVid();
    double width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height;
    //
    Future<bool> _onWillPop() async {
      AppStayles.navigateTo(context, previouspage());
      return false;
    }

    Future.delayed(Duration(milliseconds: 500), () {
      DataManager.updateProgress(20);
    });
    DataManager.canGo = false;
    return WillPopScope(
      onWillPop: _onWillPop,
      child: DisplayModule(
        Container(
          child: ListView(
            children: [
              Column(
                children: [
                  Container(
                    // color: Colors.amber,
                    child: Column(
                      children: [
                        Container(
                          margin: EdgeInsets.only(top: height * .1),
                          child: Text(
                            ModelLang.pickImgOrVid(),
                            style: AppStayles.textStyles(
                              Colors.black,
                              20.sp,
                              true,
                            ),
                          ),
                        ),
                        Container(
                          margin: EdgeInsets.only(top: height * .15),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Column(
                                children: [
                                  Row(
                                    children: [
                                      btnSize(
                                        width,
                                        Icons.photo_camera,
                                        context,
                                        ImagesPage(),
                                      ),
                                      padding(width),
                                      // textSiez(width, ModelLang.img()),
                                    ],
                                  ),
                                  Padding(
                                    padding: EdgeInsets.only(
                                      left: width * 0.20,
                                      top: width * 0.05,
                                    ),
                                  ),
                                  Row(
                                    children: [
                                      btnSizeVideo(
                                        width,
                                        Icons.video_call,
                                        context,
                                      ),
                                      padding(width),
                                    ],
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget btnSize(
    double width,
    IconData ico,
    BuildContext context,
    Object page,
  ) {
    return InkWell(
      onTap: () {
        DataManager.clear();
        AppStayles.navigateTo(context, page);
      },
      child: Container(
        width: width * 0.35,
        height: width * 0.20,
        decoration: BoxDecoration(
          gradient: AppStayles.colorGradient(),
          // color: HexColor("#7C3C21"),
          borderRadius: BorderRadius.all(Radius.circular(4.w)),
        ),
        child: Row(
          children: [
            SizedBox(
              width: width * 0.2,
              child: Icon(ico, color: Colors.white, size: 32.w),
            ),
            SizedBox(child: Icon(Icons.add, color: Colors.white, size: 35.w)),
          ],
        ),
      ),
    );
  }

  Widget btnSizeVideo(double width, IconData ico, BuildContext context) {
    return InkWell(
      onTap: () {
        DataManager.clear();
        AppStayles.navigateTo(context, VideoPage());
      },
      child: Container(
        width: width * 0.35,
        height: width * 0.20,
        decoration: BoxDecoration(
          // color: HexColor("#7C3C21"),
          gradient: AppStayles.colorGradient(),
          borderRadius: BorderRadius.all(Radius.circular(4.w)),
        ),
        child: Row(
          children: [
            SizedBox(
              width: width * 0.2,
              child: Icon(ico, color: Colors.white, size: 37.w),
            ),
            SizedBox(child: Icon(Icons.add, color: Colors.white, size: 35.w)),
          ],
        ),
      ),
    );
  }

  Widget padding(double width) {
    return Padding(padding: EdgeInsets.only(top: width * .05));
  }

  StatefulWidget? previouspage() {
    switch (DataManager.idSector) {
      case 1:
        return QrCodePage();
      case 2:
        return WmsChoose();
      case 3:
        return NavBar();
      case 4:
        return NavBar();
      case 5:
        return QrCodePage();
    }
  }
}
