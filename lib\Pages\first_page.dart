import 'dart:async';
// import 'dart:convert'; // Not used in this file
import 'dart:developer';
// import 'package:flutter/cupertino.dart'; // Not explicitly used for UI in this file
import 'package:flutter/material.dart'; // Use Material specifically
import 'package:flutter/services.dart';
// import 'package:flutter_dotenv/flutter_dotenv.dart'; // Keep if you use it elsewhere
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_switch/flutter_switch.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:marafik_app/Models/data_manager.dart';
import 'package:marafik_app/Models/lang.dart';
// import 'package:marafik_app/Models/swipe_model.dart'; // Not used in this file
import 'package:marafik_app/Styles/app_style.dart';
import 'package:marafik_app/database/fetch.dart';
// import 'package:video_player/video_player.dart'; // REMOVE this import

class FirstPage extends StatefulWidget {
  @override
  _FirstPageState createState() => _FirstPageState();
}

StreamController controller = StreamController.broadcast();

// Add SingleTickerProviderStateMixin to the State class
class _FirstPageState extends State<FirstPage>
    with SingleTickerProviderStateMixin {
  // VideoPlayerController? _controller; // REMOVE this line
  bool _visible =
      false; // Still useful for general fade of entire content if needed
  bool isDone = false;
  bool isLoaded = false; // Renamed from isLoided for clarity

  // Define AnimationController and Animation<double> for opacity and scale
  late AnimationController _animationController;
  late Animation<double> _opacityAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    FetchData.startFetchingDataTest2();

    SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

    // Initialize the AnimationController
    _animationController = AnimationController(
      vsync: this, // 'this' refers to the SingleTickerProviderStateMixin
      duration: const Duration(
        milliseconds: 1000,
      ), // Duration of the image animation
    );

    // Define the opacity animation (fades from fully transparent to fully opaque)
    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeIn, // Controls the speed curve of the fade
      ),
    );

    // Define the scale animation (scales up from slightly smaller to normal size)
    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeOut, // Controls the speed curve of the scale
      ),
    );

    // Start the animation
    _animationController.forward();

    // Mark as loaded immediately since the PNG is always "ready" to animate
    setState(() {
      isLoaded = true;
    });

    // This delay now dictates when the language selection and "start" button appear.
    // You can make it slightly longer than the image animation duration if you want a pause.
    Future.delayed(const Duration(milliseconds: 1330), () {
      setState(() {
        isDone = true;
      });
    });
  }

  @override
  void dispose() {
    _animationController
        .dispose(); // IMPORTANT: Dispose the animation controller
    super.dispose();
  }

  //////
  bool switchState = DataManager.isArabic;
  double? position; // Unused variable, consider removing if not used elsewhere

  @override
  Widget build(BuildContext context) {
    // double heightSize = MediaQuery.of(context).size.height; // Unused variable
    Future<bool> onWillPop() async {
      return false; // Prevents back button on splash screen
    }

    return SafeArea(
      child: WillPopScope(
        onWillPop: onWillPop,
        child: Scaffold(
          backgroundColor: AppStayles.fluttertoastBackgroundHexa,
          body: Padding(
            padding: EdgeInsets.symmetric(vertical: 20.h),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                  children: [
                    // Language switch
                    AnimatedOpacity(
                      opacity: isDone ? 1 : 0,
                      duration: Duration(milliseconds: 200),
                      child: Container(
                        width: MediaQuery.of(context).size.width,
                        height: MediaQuery.of(context).size.width * 0.1,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              "Français ",
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 18.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            FlutterSwitch(
                              toggleColor:
                                  AppStayles.fluttertoastBackgroundHexa,
                              activeColor: Colors.white,
                              inactiveColor: Colors.white,

                              value: switchState,
                              onToggle: (bool value) {
                                setState(() {
                                  switchState = value;
                                  DataManager.isArabic = switchState;
                                });
                              },
                            ),
                            Text(
                              " العربية",
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 20.sp,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Start button
                  ],
                ),
                // The animated PNG image as the background
                Container(
                  child:
                      isLoaded
                          ? FadeTransition(
                            // Applies the opacity animation
                            opacity: _opacityAnimation,
                            child: ScaleTransition(
                              // Applies the scale animation
                              scale: _scaleAnimation,
                              child: Image.asset(
                                width: 200.w,
                                "assets/logo_agadir.png",
                                color: Colors.white,
                                fit:
                                    BoxFit
                                        .cover, // Ensure the image covers the container
                                alignment:
                                    Alignment
                                        .center, // Center the image within its bounds
                              ),
                            ),
                          )
                          : Container(), // Placeholder while not loaded
                ),

                // Overlay content (language switch and start button)
                AnimatedOpacity(
                  opacity: isDone ? 1 : 0,
                  curve: Curves.easeInCubic,
                  duration: Duration(milliseconds: 350),
                  child: Container(
                    padding: EdgeInsets.only(bottom: 10.h),
                    width: MediaQuery.of(context).size.width,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          ModelLang.start(),
                          style: AppStayles.textStyles(
                            Colors.white,
                            20.sp,
                            true,
                          ),
                        ),
                        Icon(
                          Icons.navigate_next,
                          color: Colors.white,
                          size: 30.sp,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
