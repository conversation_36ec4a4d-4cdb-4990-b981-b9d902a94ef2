import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:marafik_app/Models/data_manager.dart';
import 'package:marafik_app/Models/display_module.dart';
import 'package:marafik_app/Models/lang.dart';
import 'package:marafik_app/Models/navbar.dart';
import 'package:marafik_app/Pages/Bus/bus_choix.dart';
import 'package:marafik_app/Pages/Wms/wms_page.dart';
import 'package:marafik_app/Pages/choix_page.dart';
import 'package:marafik_app/Pages/qr_page.dart';
import 'package:marafik_app/Styles/app_style.dart';

class GpsPage extends StatefulWidget {
  const GpsPage({Key? key}) : super(key: key);

  @override
  _GpsPageState createState() => _GpsPageState();
}

class _GpsPageState extends State<GpsPage> {
  bool presed = false;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    DataManager.previouspage = previouspage();

    Future.delayed(Duration(milliseconds: 500), () {
      DataManager.updateProgress(10);
    });
  }

  StatefulWidget? previouspage() {
    switch (DataManager.idSector) {
      case 2:
        return WmsChoose();
      case 3:
        return BusChoose();
      case 4:
        return NavBar();
    }
  }

  @override
  Widget build(BuildContext context) {
    DataManager.footerText = ModelLang.getGps();
    DataManager.canGo = false;
    return DisplayModule(
      Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(height: 100.h),
            InkWell(
              onTap: () {
                presed = true;
                setState(() {});
                Future.delayed(Duration(seconds: 1, microseconds: 500), () {
                  presed = false;
                  setState(() {});
                  AppStayles.navigateTo(context, ChoixPage());
                });
              },
              child: Container(
                width: 100.w,
                height: 100.w,
                decoration: BoxDecoration(
                  color: AppStayles.fluttertoastBackgroundHexa,
                  borderRadius: BorderRadius.all(Radius.circular(4.w)),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Container(
                      height: 70.w,
                      width: 70.w,
                      child:
                          presed
                              ? CircularProgressIndicator(
                                color: Colors.white,
                                strokeWidth: 10,
                              )
                              : Icon(
                                Icons.location_on,
                                size: 60.w,
                                color: Colors.white,
                              ),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(height: 20.h),
            Container(
              width: 320.w,
              // color: Colors.amber,
              child: Center(
                child: Text(
                  ModelLang.clickGps(),
                  textAlign: TextAlign.center,
                  style: AppStayles.textStyles(Colors.black, 18.sp, true),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
