import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:marafik_app/Models/data_manager.dart';
import 'package:marafik_app/Models/lang.dart';
import 'package:marafik_app/Models/display_module.dart';
import 'package:marafik_app/Models/models_page/empty_image_palase_holder.dart';
import 'package:marafik_app/Models/models_page/image_container.dart';
import 'package:marafik_app/Models/models_page/image_picker_handeler.dart';
import 'package:marafik_app/Pages/audio_page.dart';
import 'package:marafik_app/Pages/choix_page.dart';
import 'package:marafik_app/Styles/app_style.dart';

class ImagesPage extends StatefulWidget {
  final dynamic d;
  ImagesPage({this.d});
  @override
  _ImagesPageState createState() => _ImagesPageState();
}

class _ImagesPageState extends State<ImagesPage> {
  ImagePickerHandeler imagePickerHandeler = ImagePickerHandeler();

  void mySetState(myBool) {
    setState(() {
      DataManager.canGo = false;
    });
  }

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    DataManager.previouspage = ChoixPage();

    DataManager.canGo = false;
    Future.delayed(Duration(milliseconds: 500), () {
      DataManager.updateProgress(40);
    });
  }

  Function? myNextAction() {
    if (imagePickerHandeler.imgList.length == 0) {
      AppStayles.fluttertoast(
        ModelLang.rappelImg(),
        AppStayles.fluttertoastBackgroundHexa,
      );
    } else {
      //initializer les images
      DataManager.imageList = (imagePickerHandeler.imgList);
      // Navigator.of(context)
      //     .push(CupertinoPageRoute(builder: (context) => AudioPage()));
      AppStayles.navigateTo(context, AudioPage());
    }
    return null;
  }

  @override
  Widget build(BuildContext context) {
    DataManager.footerText = ModelLang.addImg();
    DataManager.myNextAction = myNextAction;
    imagePickerHandeler.targetSetState = mySetState;
    imagePickerHandeler.context = context;

    BoxShadow myShadow = const BoxShadow(
      color: Color.fromARGB(50, 0, 0, 0),
      offset: Offset(0, 2.7),
      spreadRadius: 1,
      blurRadius: 5,
    );
    DataManager.videoFile = null;
    double sWidth = MediaQuery.of(context).size.width;
    return DisplayModule(
      Stack(
        alignment: Alignment.center,
        children: [
          imagePickerHandeler.imgList.length != 0
              ? Positioned(
                bottom: 10.w,
                child: InkWell(
                  onTap: () {
                    if (imagePickerHandeler.imgList.isNotEmpty) {
                      DataManager.imageList = (imagePickerHandeler.imgList);
                      AppStayles.navigateTo(context, AudioPage());
                    }
                  },
                  child: Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(5.w),
                      // color: HexColor("#7C3C21"),
                      color: AppStayles.fluttertoastBackgroundHexa.withAlpha(
                        200,
                      ),
                    ),
                    width: 90.w,
                    height: 35.w,
                    child: Center(
                      child: Text(
                        ModelLang.next(),
                        style: AppStayles.textStyles(Colors.white, 18.sp, true),
                      ),
                    ),
                  ),
                ),
              )
              : Text(""),
          Align(
            alignment: Alignment.topCenter,
            child: Container(
              // color: Colors.grey,
              child: Column(
                children: [
                  Text(
                    ModelLang.addImg(),
                    style: AppStayles.textStyles(Colors.black, 18.sp, true),
                  ),
                  SizedBox(height: 7.w),
                  imagePickerHandeler.imgList.isNotEmpty
                      ? (imagePickerHandeler.imgList.length == 1
                          ? Container(
                            padding: EdgeInsets.only(
                              left: 108.w,
                              right: 108.w,
                              bottom: 8.w,
                            ),
                            child: Column(
                              children: [
                                Container(
                                  width: 158.w,
                                  height: 158.w,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(4.w),
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(4.w),
                                    child: Image.file(
                                      imagePickerHandeler.imgList.last,
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                                SizedBox(height: 10.w),
                                Container(
                                  child: Icon(
                                    Icons.add,
                                    color: Colors.white,
                                    size: 20.w,
                                  ),
                                  height: 36.w,
                                  width: 36.w,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: AppStayles.fluttertoastBackgroundHexa
                                        .withAlpha(200),
                                    boxShadow: [myShadow],
                                  ),
                                ),
                              ],
                            ),
                          )
                          : Container(
                            // color: Colors.amber,
                            padding: EdgeInsets.only(
                              left: 108.w,
                              right: 108.w,
                              bottom: 8.w,
                            ),
                            child: Column(
                              children: [
                                Container(
                                  width: 158.w,
                                  height: 158.w,
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(4.w),
                                  ),
                                  child: ClipRRect(
                                    borderRadius: BorderRadius.circular(4.w),
                                    child: Image.file(
                                      imagePickerHandeler.imgList.last,
                                      fit: BoxFit.cover,
                                    ),
                                  ),
                                ),
                                SizedBox(height: 10.w),
                                InkWell(
                                  overlayColor: MaterialStateProperty.all(
                                    Colors.transparent,
                                  ),
                                  onTap:
                                      () async =>
                                          imagePickerHandeler.pickercamera(),
                                  child: Container(
                                    child: Icon(Icons.add, color: Colors.white),
                                    height: 36.w,
                                    width: 36.w,
                                    decoration: BoxDecoration(
                                      shape: BoxShape.circle,
                                      color: AppStayles
                                          .fluttertoastBackgroundHexa
                                          .withAlpha(200),
                                      boxShadow: [myShadow],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ))
                      : Container(
                        padding: EdgeInsets.only(
                          left: 108.w,
                          right: 108.w,
                          bottom: 8.w,
                        ),
                        child: Column(
                          children: [
                            InkWell(
                              child: Container(
                                child: Icon(
                                  Icons.add,
                                  size: 97.w,
                                  color: Colors.white,
                                ),
                                width: 158.w,
                                height: 158.w,
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(4.w),
                                ),
                              ),
                              onTap:
                                  () async =>
                                      imagePickerHandeler.pickercamera(),
                            ),
                            SizedBox(height: 46.w),
                          ],
                        ),
                      ),
                  Container(
                    // color: Colors.amber,
                    height: 100.w,
                    child: ListView.builder(
                      shrinkWrap: true,
                      scrollDirection: Axis.horizontal,
                      itemCount: 5,
                      itemBuilder: (context, i) {
                        return Container(
                          padding: EdgeInsets.symmetric(horizontal: 8.w),
                          child:
                              imagePickerHandeler.imgList.length > i
                                  ? ImageContainer(
                                    i + 1,
                                    imagePickerHandeler.imgList[i],
                                    ModelLang.imgPack() +
                                        " " +
                                        (i + 1).toString(),
                                    fn: () {
                                      imagePickerHandeler.removeImage(
                                        imagePickerHandeler.imgList[i],
                                      );
                                    },
                                  )
                                  : EmptyImagePlaceHolder(index: i + 1),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
      myBoolbool: true,
    );
  }
}
