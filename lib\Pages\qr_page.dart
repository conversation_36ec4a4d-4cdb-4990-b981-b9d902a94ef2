import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:marafik_app/Models/data_manager.dart';
import 'package:marafik_app/Models/display_module.dart';
import 'package:marafik_app/Models/lang.dart';
import 'package:marafik_app/Models/navbar.dart';
import 'package:marafik_app/Models/qr_view.dart';
import 'package:marafik_app/Models/swipe_model.dart';
import 'package:marafik_app/Pages/Bus/bus_choix.dart';
import 'package:marafik_app/Pages/choix_page.dart';
import 'package:marafik_app/Pages/Home/second_page.dart';
import 'package:marafik_app/Pages/wms/wms_page.dart';
import 'package:marafik_app/Styles/app_style.dart';

class QrCodePage extends StatefulWidget {
  const QrCodePage({Key? key}) : super(key: key);

  @override
  _QrCodePageState createState() => _QrCodePageState();
}

class _QrCodePageState extends State<QrCodePage> {
  Future<bool> _onWillPop() async {
    AppStayles.navigateTo(context, previouspage());
    return false;
  }

  @override
  void initState() {
    // TODO: implement initState
    // DataManager.myNextAction = myNextAction;
    DataManager.canGo = false;
    super.initState();
    DataManager.previouspage = previouspage();

    Future.delayed(Duration(milliseconds: 500), () {
      DataManager.updateProgress(10);
    });
  }

  //  Function myNextAction() {
  //   if (imagePickerHandeler.imgList.length == 0) {
  //     AppStayles.fluttertoast(ModelLang.rappelImg(), HexColor("#F7931E"));
  //   } else {
  //     //initializer les images
  //     DataManager.imageList = (imagePickerHandeler.imgList);
  //     // Navigator.of(context)
  //     //     .push(CupertinoPageRoute(builder: (context) => AudioPage()));
  //   }
  // }

  showAlertDialog(BuildContext context) {
    Widget okButton = TextButton(
      child: Text(ModelLang.ok()),
      onPressed: () {
        Navigator.of(context).pop();
      },
    );

    // set up the AlertDialog
    AlertDialog alert = AlertDialog(
      title: Text(ModelLang.connectionState()),
      content: Text(ModelLang.noCnx()),
      actions: [okButton],
    );

    // show the dialog
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return alert;
      },
    );
  }

  double pos = 400;
  bool selected = false;

  @override
  Widget build(BuildContext context) {
    DataManager.footerText = ModelLang.qrScan();

    double width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height;

    double r = 4.w;
    // double bw = 20;
    // Color borderColor = HexColor("F58B8B");
    // Color qrColorBar = HexColor("#D53434");
    // double scl = 2.5;
    //
    Future.delayed(const Duration(milliseconds: 500), () {
      setState(() {
        selected = true;
      });
    });

    return WillPopScope(
      onWillPop: _onWillPop,
      child: DisplayModule(
        Container(
          // color: Colors.amber,
          // height: 190.h,
          width: MediaQuery.of(context).size.width,
          child: Stack(
            alignment: Alignment.center,
            children: [
              AnimatedPositioned(
                curve: Curves.easeOutQuart,
                duration: Duration(milliseconds: 600),
                top: selected ? 150.w : height,
                child: Container(
                  height: width * 0.8,
                  width: width * 0.35,
                  child: Column(
                    children: [
                      InkWell(
                        onTap: () {
                          //  getPermition();
                          AppStayles.navigateTo(context, QrView());
                        },
                        child: Container(
                          height: width * 0.35,
                          width: width * 0.35,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(r),
                            color: AppStayles.fluttertoastBackgroundHexa,
                          ),
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Image.asset(
                                "assets/qrCode.png",
                                height: width * 0.2,
                                width: width * 0.2,
                              ),
                              // Container(
                              //   height: width * 0.2,
                              //   width: width * 0.2,
                              //   decoration: BoxDecoration(
                              //     image: DecorationImage(
                              //       image: AssetImage("assets/qrCode.png"),
                              //     ),
                              //   ),
                              // ),
                            ],
                          ),
                        ),
                      ),
                      SizedBox(height: 20.w),
                      Text(
                        ModelLang.click(),
                        style: AppStayles.textStyles(Colors.black, 18.sp, true),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  StatefulWidget? previouspage() {
    switch (DataManager.idSector) {
      case 1:
        return NavBar();
      case 2:
        return WmsChoose();
      case 3:
        return NavBar();
      case 4:
        return NavBar();
      case 5:
        return NavBar();
    }
  }
}
