import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:marafik_app/Models/data_manager.dart';
import 'package:marafik_app/Models/lang.dart';
import 'package:marafik_app/Models/location.dart';
import 'package:marafik_app/Models/navbar.dart';
import 'package:marafik_app/Pages/Home/second_page.dart';
import 'package:marafik_app/Styles/app_style.dart';
import 'package:marafik_app/jira/jira_api_call.dart';

class Succes extends StatefulWidget {
  @override
  _SuccesState createState() => _SuccesState();
}

class _SuccesState extends State<Succes> {
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _handleSubmitted();
    Future.delayed(Duration(milliseconds: 500), () {
      DataManager.updateProgress(100);
    });
  }

  _handleSubmitted() async {
    await JiraApiCall.upload(
      context,
      "position : " + LocationHandel.constractUrl(),
    );
    // AppStayles.navigateTo(context, NavBar());
  }

  @override
  Widget build(BuildContext context) {
    Future<bool> onWillPop() async {
      return false;
    }

    return WillPopScope(
      onWillPop: onWillPop,
      child: Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 120,
                height: 120,
                decoration: const BoxDecoration(
                  color: Colors.green,
                  // gradient: AppStayles.colorGradient(),
                  borderRadius: BorderRadius.all(Radius.circular(100)),
                  boxShadow: [
                    BoxShadow(
                      color: Color.fromARGB(50, 0, 0, 0),
                      offset: Offset(0, 3),
                      spreadRadius: 2,
                      blurRadius: 10,
                    ),
                  ],
                ),
                child: Image.asset(
                  color: AppStayles.fluttertoastBackgroundHexa,
                  "assets/checkIcon.png",
                  scale: 4,
                ),

                // Container(
                //   decoration: BoxDecoration(
                //     image: DecorationImage(
                //       image: AssetImage("assets/checkIcon.png"),
                //       scale: 4,
                //     ),
                //   ),
                // ),
              ),
              SizedBox(height: 30),
              Container(
                width: 450,
                child: Center(
                  child: Text(
                    ModelLang.succEnrg(),
                    textAlign: TextAlign.center,
                    style: AppStayles.textStyles(
                      Colors.grey[700]!,
                      20.sp,
                      true,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        floatingActionButton: Container(
          width: 90.w,
          height: 35.w,
          decoration: BoxDecoration(
            // color: HexColor("#f3a683"),
            borderRadius: BorderRadius.circular(5.w),
          ),
          child: FloatingActionButton.extended(
            label: Center(
              child: Text(
                ModelLang.cancel(),
                textAlign: TextAlign.center,
                style: AppStayles.textStyles(Colors.white, 18.sp, true),
              ),
            ),
            onPressed: () {
              AppStayles.navigateTo(context, NavBar());
            },
            backgroundColor: AppStayles.fluttertoastBackgroundHexa.withAlpha(
              200,
            ),
          ),
        ),
        floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
      ),
    );
  }
}
