import 'dart:io';
import 'dart:ui';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:image_picker/image_picker.dart';
import 'package:marafik_app/Models/data_manager.dart';
import 'package:marafik_app/Models/display_module.dart';
import 'package:marafik_app/Models/lang.dart';
import 'package:marafik_app/Models/location.dart';
import 'package:marafik_app/Pages/choix_page.dart';
import 'package:marafik_app/Pages/succes_page.dart';
import 'package:marafik_app/Styles/app_style.dart';
import 'package:video_player/video_player.dart';

class VideoPage extends StatefulWidget {
  @override
  _VideoPageState createState() => _VideoPageState();
}

class _VideoPageState extends State<VideoPage> {
  File? path;
  VideoPlayerController? controller;

  void _showPhotocamera() async {
    DataManager.myNextAction = myNextAction;
    // if (!DataManager.location) {
    //   show();
    //   LocationHandel.requestLocationService().then((result) {
    //     if (!result) {
    //       Navigator.of(context).pop();
    //       showDialog(
    //         context: context,
    //         builder: (BuildContext context) {
    //           return InfoDialog(
    //               title: ModelLang.alert(),
    //               description: ModelLang.rappelGps(),
    //               afterCancel: () {});
    //         },
    //       );
    //     } else {
    //       DataManager.location = true;
    //       LocationHandel.cachLocation();
    //       Navigator.of(context).pop();
    //       pickVideo();
    //     }
    //   });
    // } else {
    LocationHandel.getLocation(context, pickVideo);
    // }
  }

  pickVideo() async {
    final myfile = await ImagePicker().pickVideo(source: ImageSource.camera);
    if (myfile != null) {
      setState(() {
        path = File(myfile.path);
        controller = VideoPlayerController.file(path!)
          ..initialize().then((_) {
            setState(() {});
          });
        DataManager.videoFile = path;
        DataManager.imageList = [];
        DataManager.canGo = true;
      });
    }
  }

  show() {
    showDialog(
      context: context,
      builder: (_) {
        return AlertDialog(title: Text(ModelLang.waitAlert()));
      },
    ).then((exit) {
      if (exit == null) return;

      if (exit) {
        // user pressed Yes button
      } else {
        // user pressed No button
      }
    });
  }

  Function? myNextAction() {
    AppStayles.navigateTo(context, Succes());
    return null;
  }

  @override
  void initState() {
    // TODO: implement initState
    DataManager.canGo = false;
    super.initState();
    DataManager.previouspage = ChoixPage();
    Future.delayed(Duration(milliseconds: 500), () {
      DataManager.updateProgress(80);
    });
  }

  @override
  Widget build(BuildContext context) {
    double width = MediaQuery.of(context).size.width;
    double height = MediaQuery.of(context).size.height;

    DataManager.footerText = ModelLang.video();

    return DisplayModule(
      Container(
        child: SingleChildScrollView(
          child: Column(
            children: [
              // SizedBox(
              //   height: height * 0.20,
              // ),
              Container(
                margin: EdgeInsets.only(bottom: 20),
                child: Text(
                  path == null ? ModelLang.video() : ModelLang.watchVideo(),
                  style: AppStayles.textStyles(Colors.black, 18, true),
                ),
              ),
              path == null
                  ? Center(
                    child: InkWell(
                      child: Container(
                        width:
                            MediaQuery.of(context).size.height *
                            0.5 *
                            (controller == null ||
                                    controller!.value.aspectRatio < 1
                                ? 0.5625
                                : 1),
                        height:
                            MediaQuery.of(context).size.height *
                            0.5 *
                            (controller == null ||
                                    controller!.value.aspectRatio < 1
                                ? 1
                                : 0.5625),
                        decoration: BoxDecoration(
                          color: HexColor("#EEEEEE"),
                          borderRadius: BorderRadius.only(
                            topRight: Radius.circular(4.w),
                            topLeft: Radius.circular(4.w),
                            bottomLeft: Radius.circular(4.w),
                          ),
                        ),
                        child: Icon(Icons.add, color: Colors.white, size: 100),
                      ),
                      onTap: () {
                        _showPhotocamera();
                      },
                    ),
                  )
                  : Center(
                    child: Column(
                      children: [
                        Container(
                          width:
                              MediaQuery.of(context).size.height *
                              0.5 *
                              (controller!.value.aspectRatio < 1 ? 0.5625 : 1),
                          height:
                              MediaQuery.of(context).size.height *
                              0.45 *
                              (controller!.value.aspectRatio < 1 ? 1 : 0.5625),
                          child: AspectRatio(
                            aspectRatio: controller!.value.aspectRatio,
                            child: Stack(
                              alignment: Alignment.bottomCenter,
                              children: <Widget>[
                                ClipRRect(
                                  child: VideoPlayer(controller!),
                                  borderRadius: BorderRadius.only(
                                    topRight: Radius.circular(4.w),
                                    topLeft: Radius.circular(4.w),
                                  ),
                                ),
                                // ClosedCaption(
                                //     text: controller.value.caption.text),
                                ClipRRect(
                                  child: ControlsOverlay(
                                    controller: controller!,
                                  ),
                                  borderRadius: BorderRadius.only(
                                    topRight: Radius.circular(4.w),
                                    topLeft: Radius.circular(4.w),
                                  ),
                                ),
                                VideoProgressIndicator(
                                  controller!,
                                  allowScrubbing: true,
                                ),
                              ],
                            ),
                          ),
                        ),
                        SizedBox(height: height * 0.02),
                        InkWell(
                          onTap: () {
                            path = null;
                            controller!.dispose();
                            setState(() {
                              DataManager.canGo = false;
                            });
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              boxShadow: [
                                BoxShadow(
                                  color: Color.fromARGB(50, 0, 0, 0),
                                  offset: Offset(0, 2.7),
                                  spreadRadius: 1,
                                  blurRadius: 5,
                                ),
                              ],
                              color: HexColor("#D53434"),
                              borderRadius: BorderRadius.circular(100),
                            ),
                            height: width * 0.12,
                            width: width * 0.12,
                            child: Icon(Icons.clear, color: Colors.white),
                          ),
                        ),
                      ],
                    ),
                  ),
            ],
          ),
        ),
      ),
      // floatingActionButton: path != null
      //     ? Container(
      //         width: MediaQuery.of(context).size.width * 0.45,
      //         child: FloatingActionButton.extended(
      //           label: Text(ModelLang.next(),
      //               style: AppStayles.textStyles(Colors.white, 18, true)),
      //           icon: Icon(
      //             Icons.navigate_next,
      //             color: Colors.white,
      //             size: 30,
      //           ),
      //           onPressed: () {
      //             if (path == null) {
      //               AppStayles.fluttertoast(
      //                   ModelLang.rappelImg(), HexColor("#F7931E"));
      //             } else {
      //               Navigator.of(context).push(
      //                   CupertinoPageRoute(builder: (context) => FinalPage()));
      //             }
      //           },
      //           backgroundColor: HexColor("#D53434"),
      //         ),
      //       )
      //     : null,
      // floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }
}

class ControlsOverlay extends StatefulWidget {
  const ControlsOverlay({Key? key, this.controller}) : super(key: key);

  final VideoPlayerController? controller;

  @override
  _ControlsOverlayState createState() => _ControlsOverlayState();
}

class _ControlsOverlayState extends State<ControlsOverlay> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      children: <Widget>[
        AnimatedSwitcher(
          duration: Duration(milliseconds: 50),
          reverseDuration: Duration(milliseconds: 200),
          child:
              widget.controller!.value.isPlaying
                  ? SizedBox.shrink()
                  : Container(
                    color: Colors.black26,
                    child: Center(
                      child: Icon(
                        Icons.play_arrow,
                        color: Colors.white,
                        size: 100.0,
                      ),
                    ),
                  ),
        ),
        GestureDetector(
          onTap: () {
            setState(() {
              widget.controller!.value.isPlaying
                  ? widget.controller!.pause()
                  : widget.controller!.play();
            });
          },
        ),
      ],
    );
  }
}
