import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:marafik_app/Models/lang.dart';

class SectorCard extends StatelessWidget {
  final String title;
  final String description;
  final String imagePath;
  final VoidCallback onTap;
  final int sectorIndex;

  const SectorCard({
    Key? key,
    required this.title,
    required this.description,
    required this.imagePath,
    required this.onTap,
    required this.sectorIndex,
  }) : super(key: key);

  // Gradient color schemes for each sector
  static const List<List<Color>> sectorGradients = [
    [Color(0xFF60A5FA), Color(0xFF3B82F6)], // Blue gradient for PCP (Markets)
    [
      Color(0xFF34D399),
      Color(0xFF10B981),
    ], // Green gradient for WMS (Waste Management)
    [Color(0xFFF87171), Color(0xFFEF4444)], // Red gradient for Transport (Bus)
    [
      Color(0xFF4ADE80),
      Color(0xFF22C55E),
    ], // Light Green gradient for Espace Vert (Green Spaces)
    [
      Color(0xFFA78BFA),
      Color(0xFF8B5CF6),
    ], // Purple gradient for Abattoir (Slaughterhouse)
    [
      Color(0xFF67E8F9),
      Color(0xFF06B6D4),
    ], // Cyan gradient for Eau et Assainissement (Water)
    [
      Color(0xFFFBBF24),
      Color(0xFFF59E0B),
    ], // Orange gradient for Voirie (Roads)
    [
      Color(0xFF818CF8),
      Color(0xFF6366F1),
    ], // Indigo gradient for Sécurité (Security)
  ];

  List<Color> get sectorGradient =>
      sectorGradients[sectorIndex % sectorGradients.length];

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 6.h),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16.r),
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(16.r),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.06),
                  blurRadius: 10.r,
                  offset: Offset(0, 4.h),
                  spreadRadius: 0,
                ),
              ],
              border: Border.all(color: Colors.grey.shade100, width: 1.w),
            ),
            child: Padding(
              padding: EdgeInsets.all(16.w),
              child: Column(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Icon Container with sector-specific gradient
                  Container(
                    width: 55.w,
                    height: 55.w,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: sectorGradient,
                      ),
                      borderRadius: BorderRadius.circular(12.r),
                      boxShadow: [
                        BoxShadow(
                          color: sectorGradient[1].withOpacity(0.3),
                          blurRadius: 8.r,
                          offset: Offset(0, 4.h),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: EdgeInsets.all(10.w),
                      child: Image.asset(
                        imagePath,
                        fit: BoxFit.contain,
                        color: Colors.white,
                      ),
                    ),
                  ),

                  SizedBox(height: 12.h),

                  // Title Section - Takes needed space
                  Text(
                    title,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: 15.sp,
                      fontWeight: FontWeight.w700,
                      color: Colors.black87,
                      letterSpacing: -0.3,
                      height: 1.2,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  SizedBox(height: 8.h),

                  // Description Section - Flexible to fill available space
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      child: Text(
                        description,
                        textAlign: TextAlign.center,
                        style: TextStyle(
                          fontSize: 11.5.sp,
                          color: Colors.grey.shade600,
                          height: 1.3,
                          letterSpacing: 0.1,
                        ),
                        maxLines: 3,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ),

                  SizedBox(height: 12.h),

                  // Action Button with sector gradient
                  Container(
                    width: double.infinity,
                    height: 32.h,
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: sectorGradient,
                      ),
                      borderRadius: BorderRadius.circular(8.r),
                      boxShadow: [
                        BoxShadow(
                          color: sectorGradient[1].withOpacity(0.4),
                          blurRadius: 6.r,
                          offset: Offset(0, 3.h),
                        ),
                      ],
                    ),
                    child: Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: onTap,
                        borderRadius: BorderRadius.circular(8.r),
                        child: Center(
                          child: Text(
                            ModelLang.reclamation(),
                            style: TextStyle(
                              fontSize: 12.sp,
                              fontWeight: FontWeight.w600,
                              color: Colors.white,
                              letterSpacing: 0.2,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
