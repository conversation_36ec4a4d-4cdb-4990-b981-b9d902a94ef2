import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:hexcolor/hexcolor.dart';
import 'package:marafik_app/Models/data_manager.dart';
import 'package:marafik_app/Models/lang.dart';
import 'package:marafik_app/Models/navbar.dart';
import 'package:marafik_app/Models/swipe_model.dart';
import 'package:marafik_app/Pages/Wms/wms_page.dart';
import 'package:marafik_app/Pages/qr_page.dart';
import 'package:marafik_app/Pages/Home/second_page.dart';

class AppStayles {
  static Color fluttertoastBackground = Colors.red;
  static Color fluttertoastTextColor = Colors.white;
  static Color fluttertoastBackgroundHexa = Color(0xff235ea8);

  static TextStyle textStyles(Color c, double fontSize, bool bold) {
    return TextStyle(
      // fontFamily: "Roboto Regular",
      color: c,
      fontSize: DataManager.isArabic ? fontSize + 2 : fontSize,
      fontWeight: DataManager.isArabic ? FontWeight.bold : FontWeight.w500,
    );
  }

  // static showAlertDialogNoCNX(BuildContext context) {
  //   Widget okButton = TextButton(
  //     child: Text(ModelLang.ok()),
  //     onPressed: () {
  //       Navigator.of(context, rootNavigator: true).pop();
  //       navigateTo(context, FirstModel());
  //     },
  //   );

  //   // set up the AlertDialog
  //   AlertDialog alert = AlertDialog(
  //     title: Text(ModelLang.connectionState()),
  //     content: Text(ModelLang.noCnx()),
  //     actions: [
  //       okButton,
  //     ],
  //   );

  //   // show the dialog
  //   showDialog(
  //     context: context,
  //     builder: (BuildContext context) {
  //       return alert;
  //     },
  //   );
  // }

  static BoxDecoration boxDecorationImage(String filePath) {
    return BoxDecoration(
      image: DecorationImage(image: AssetImage(filePath), fit: BoxFit.contain),
    );
  }

  static void navigateTo(BuildContext context, dynamic page) {
    Navigator.of(context).push(MaterialPageRoute(builder: (context) => page));
  }

  static dynamic fluttertoast(String msg, Color c) {
    return Fluttertoast.showToast(
      msg: msg,
      toastLength: Toast.LENGTH_SHORT,
      gravity: ToastGravity.BOTTOM,
      timeInSecForIosWeb: 2,
      backgroundColor: c,
      textColor: Colors.white,
      fontSize: 16.0,
    );
  }

  static PreferredSizeWidget appbar(BuildContext context) {
    return AppBar(
      backgroundColor: AppStayles.fluttertoastBackgroundHexa,
      elevation: 2.5,
      centerTitle: true,
      automaticallyImplyLeading: false,
      leading: IconButton(
        icon: Icon(Icons.arrow_back, size: 35, color: Colors.white),
        onPressed: () {
          StatefulWidget? previouspage() {
            switch (DataManager.idSector) {
              case 1:
                return QrCodePage();
              case 2:
                return QrCodePage();
              case 3:
                return NavBar();
              case 4:
                return NavBar();
              case 5:
                return QrCodePage();
            }
          }

          // Navigator.of(context).pop();
          // Navigator.pop(context,true);

          navigateTo(context, DataManager.previouspage);
          // Navigator.of(context, rootNavigator: true).pop();
        },
      ),
      title: Text(
        DataManager.nomSector != "" ? DataManager.nomSector : "",
        style: AppStayles.textStyles(Colors.white, 18.sp, true),
      ),
      actions: [
        IconButton(
          icon: Icon(Icons.home, size: 35, color: Colors.white),
          onPressed: () {
            AppStayles.navigateTo(context, NavBar());
          },
        ),
        SizedBox(width: 10.w),
      ],
    );
  }

  static PreferredSizeWidget appbarRlt(BuildContext context) {
    return AppBar(
      backgroundColor: AppStayles.fluttertoastBackgroundHexa,
      title: Text(DataManager.appName),
      elevation: 19.0,
      centerTitle: true,
      automaticallyImplyLeading: false,
      actions: [
        IconButton(
          icon: Icon(Icons.arrow_forward, size: 35, color: Colors.white),
          onPressed: () {
            Navigator.of(context).pop();
          },
        ),
      ],
    );
  }

  static dynamic shadow() {
    return [
      const BoxShadow(
        color: Color.fromARGB(50, 0, 0, 0),
        offset: Offset(0, 2.7),
        spreadRadius: 1,
        blurRadius: 5,
      ),
    ];
  }

  static LinearGradient colorGradient() {
    return LinearGradient(
      colors: [
        AppStayles.fluttertoastBackgroundHexa,
        AppStayles.fluttertoastBackgroundHexa.withAlpha(200),
      ],
      begin: const FractionalOffset(0.0, 1.0),
      end: const FractionalOffset(0.5, 0.0),
      stops: [0.0, 1.0],
      tileMode: TileMode.clamp,
    );
  }

  static myReportDialog(
    BuildContext context,
    String popTitle,
    String content,
    String image,
    page,
  ) {
    return showDialog(
      context: context,
      builder: (context) {
        return Dialog(
          backgroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10.w),
          ),
          child: Stack(
            children: [
              Container(
                height: 300.w,
                child: Stack(
                  children: [
                    Align(
                      alignment: Alignment.topCenter,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            children: [
                              IconButton(
                                onPressed: () {
                                  Navigator.of(context).pop();
                                },
                                icon: Icon(Icons.close),
                                iconSize: 15.w,
                              ),
                            ],
                          ),
                          // Container(
                          //   height: 60.w,
                          //   width: 60.w,
                          //   decoration: BoxDecoration(
                          //     image: DecorationImage(
                          //       image: AssetImage(image),
                          //       fit: BoxFit.cover,
                          //     ),
                          //   ),
                          // ),
                          Image.asset(
                            color: AppStayles.fluttertoastBackgroundHexa,
                            image,
                            height: 60.w,
                            width: 60.w,
                            fit: BoxFit.cover,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Container(
                                width: 250.w,
                                height:
                                    //ScreenUtil().setHeight(30) ,
                                    30.w,
                                // color: Colors.amber,
                                child: Text(
                                  popTitle,
                                  overflow: TextOverflow.visible,
                                  style: textStyles(Colors.black, 16.sp, false),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ],
                          ),
                          Container(
                            width: 244.w,
                            height: 100.w,
                            // padding: EdgeInsets.all(10.w),
                            //  color: Colors.amber,
                            child: Center(
                              child: Text(
                                content,
                                textAlign: TextAlign.center,
                                // textAlign: DataManager.isArabic
                                //     ? TextAlign.center
                                //     : TextAlign.center,
                                style: textStyles(Colors.black54, 16.sp, false),
                              ),
                            ),
                          ),
                          SizedBox(height: 28.w),
                        ],
                      ),
                    ),
                    Align(
                      alignment: Alignment.bottomCenter,
                      child: Container(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: [
                            Container(
                              color: Color(0xffE6E6E6),
                              child: Row(
                                children: [
                                  Expanded(
                                    child: SizedBox(width: 10.w, height: 1.w),
                                  ),
                                ],
                              ),
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Expanded(
                                  flex: 1,
                                  child: Container(
                                    height: 50.w,
                                    // width: 139.w,
                                    // color: Colors.amber,
                                    child: popupButton(
                                      context,
                                      ModelLang.cancel(),
                                      false,
                                    ),
                                  ),
                                ),
                                //virtical line
                                Container(
                                  color: Color(0xffE6E6E6),
                                  child: SizedBox(width: 1.w, height: 20.w),
                                ),
                                Expanded(
                                  flex: 1,
                                  child: Container(
                                    height: 50.w,
                                    child: popupButton(
                                      context,
                                      ModelLang.report(),
                                      true,
                                      page: page,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  static Widget popupButton(
    BuildContext context,
    String txt,
    bool isBold, {
    page,
  }) {
    return Container(
      height: 35.w,
      child: InkWell(
        child:
            isBold
                ? Center(
                  child: Text(
                    txt,
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Color(0xff408E80),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                )
                : Center(
                  child: Text(
                    txt,
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Color(0xff408E80),
                      fontWeight: FontWeight.normal,
                    ),
                  ),
                ),
        // ),
        onTap: () {
          if (isBold) {
            Navigator.of(context).pop();
            DataManager.curentProgress = 0;
            navigateTo(context, page);
            print("report");
          } else {
            Navigator.of(context).pop();
          }
        },
      ),
    );
  }

  static showAlertDialogSimple(
    BuildContext context,
    String ok,
    String state,
    String body,
    bool isCnx,
  ) {
    Widget okButton = TextButton(
      child: Center(
        child: Container(
          width: 80.w,
          decoration: BoxDecoration(
            color: AppStayles.fluttertoastBackgroundHexa,
            borderRadius: BorderRadius.circular(4.w),
          ),
          child: Center(
            child: Text(
              ok,
              style: AppStayles.textStyles(Colors.white, 18.sp, true),
            ),
          ),
        ),
      ),
      onPressed: () {
        if (isCnx) {
          Navigator.of(context, rootNavigator: true).pop();
          navigateTo(context, FirstModel());
        } else {
          Navigator.of(context, rootNavigator: true).pop();
        }
      },
    );
    AlertDialog alert = AlertDialog(
      title: Center(
        child: Text(
          state,
          style: AppStayles.textStyles(Colors.redAccent, 18.sp, true),
        ),
      ),
      content: Container(
        height: 50.h,
        child: Center(
          child: Text(
            body,
            style: AppStayles.textStyles(Colors.black, 18.sp, true),
          ),
        ),
      ),
      actions: [okButton],
    );
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return alert;
      },
    );
  }

  //  TextStyle textStyles(Color c, double fontSize, bool bold) {
  //   return
  //   TextStyle(
  //       color: c,
  //       fontSize: DataManager.isArabic ? fontSize + 2 : fontSize,
  //       fontWeight: bold? FontWeight.w500 : FontWeight.bold,
  //       fontFamily: bold? 'Roboto' : 'MontserratExtraLight'
  //       )
  //       ;
  // }
}

class InfoDialog extends StatelessWidget {
  final title;
  final description;
  final afterCancel;
  const InfoDialog({Key? key, this.title, this.description, this.afterCancel})
    : super(key: key);

  @override
  build(BuildContext context) {
    final size = MediaQuery.of(context).size;
    final screenWidth = size.width;
    final screenHeight = size.height;

    return Dialog(
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(40)),
      elevation: 5.0,
      backgroundColor: Colors.transparent,
      child: Container(
        width: screenWidth * 0.8,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(40),
          color: Colors.white,
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(vertical: 25),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Padding(
                padding: const EdgeInsets.all(5),
                child: Text(
                  title,
                  style: TextStyle(
                    color: Color(0xff263949),
                    fontSize: 35,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Padding(
                padding: EdgeInsets.fromLTRB(10, 5, 10, 5),
                child: Container(
                  child: Text(
                    description,
                    style: TextStyle(
                      color: Color(0xff263949),
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                    maxLines: 5,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ),
              SizedBox(height: screenHeight * 0.03),
              Center(
                child: InkWell(
                  onTap: () {
                    afterCancel();
                    Navigator.of(context).pop();
                  },
                  child: Container(
                    width: 90,
                    height: 50,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(21),
                      color: AppStayles.fluttertoastBackgroundHexa,
                      boxShadow: [
                        BoxShadow(
                          color: Color.fromARGB(50, 0, 0, 0),
                          offset: Offset(0, 2),
                          spreadRadius: 1,
                          blurRadius: 10,
                        ),
                      ],
                    ),
                    child: Center(
                      child: SizedBox(
                        width: 40,
                        height: 40,
                        child: FittedBox(
                          fit: BoxFit.fitWidth,
                          child: Text(
                            ModelLang.ok(),
                            style: AppStayles.textStyles(
                              Color(0xff263949),
                              18,
                              true,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
