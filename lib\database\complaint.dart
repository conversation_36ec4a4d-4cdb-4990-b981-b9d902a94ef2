class Complaint {
  int id = -1;
  String linkLocation = "";
  String description = "";
  String idJira = "null";
  String isSent = "true";
  String state = "En attente";
  String nameSecteur = "";
  String date =
      DateTime.now().day.toString() +
      '-' +
      DateTime.now().month.toString() +
      '-' +
      DateTime.now().year.toString();
  int idSector = -1;
  Map<String, dynamic> toMap() => {
    'linkLocation': linkLocation,
    'description': description,
    'idJira': idJira,
    'isSent': isSent,
    'state': state,
    'dateC': date,
    'idSector': idSector,
  };
  Complaint.creat(this.linkLocation, this.description, this.idSector);
  Complaint(
    this.id,
    this.linkLocation,
    this.description,
    this.idJira,
    this.isSent,
    this.state,
    this.date,
    this.nameSecteur,
    this.idSector,
  );
}

List<Complaint> sampleComplaints = [
  Complaint(
    1,
    "https://example.com/location1",
    "Water leakage near building A",
    "null",
    "true",
    "En attente",
    "01-08-2025",
    "Plumbing",
    101,
  ),
  Complaint(
    2,
    "https://example.com/location2",
    "Broken street light on 5th avenue",
    "JIRA-002",
    "true",
    "En cours",
    "28-07-2025",
    "Electricity",
    102,
  ),
  Complaint(
    3,
    "https://example.com/location3",
    "Overflowing garbage near school",
    "JIRA-003",
    "false",
    "Résolu",
    "25-07-2025",
    "Sanitation",
    103,
  ),
  Complaint(
    4,
    "https://example.com/location4",
    "Potholes in the main road",
    "JIRA-004",
    "true",
    "En attente",
    "29-07-2025",
    "Roads",
    104,
  ),
];
