import 'dart:async';
import 'dart:convert';
import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';
import 'package:http/http.dart' as http;
import 'package:http/http.dart';
import 'package:marafik_app/Models/check_connection.dart';
import 'package:marafik_app/config/config_urls.dart';
import 'package:marafik_app/database/complaint.dart';
import 'package:marafik_app/database/sqlite.dart';
import 'package:marafik_app/jira/projet_jira.dart';

class FetchData {
  static DbHelper db = new DbHelper();
  static addFile(http.MultipartRequest req, File file) async {
    Uint8List data = await file.readAsBytes();
    List<int> list = data.cast();
    req.files.addAll({
      http.MultipartFile.fromBytes(
        'file',
        list,
        filename: DateTime.now().toString(),
      ),
    });
  }

  static bool isFetching = false;
  static Future<void> startFetchingDataTest2() async {
    print(
      "********************************* 1 Fetch I ******************************************",
    );
    // if (!isFetching) {
    isFetching = true;
    if (await CheckConnection.isConnected()) {
      // Complaint c = await db.getFirstComplaint();
      Complaint c = sampleComplaints[0];
      while (c != null) {
        print("@@@@@@@@@@@@@@@is fetchingggggggggggggggggggg @@@@@@@@@@@@@@@");

        if (await CheckConnection.isConnected()) {
          String idJira = "";
          //print("EEEEEEEEEEE ${c.idJira} EEEEEEEEEEEEEEEEEEE");
          if (c.idJira == "null") {
            var url = Uri.parse(ConfigUrls.jiraUrl + "issue");

            // print("========= ${c.idSector} =================");
            // Sector sector = await db.getSectorById(c.idSector);
            // print("========= ${sector.nameFr} =================");

            var body = jsonEncode({
              "fields": {
                "project": {
                  "self": ConfigUrls.jiraUrl + "project/" + ProJira.nProjet,
                  "id": "${ProJira.nProjet}",
                  "key": "${ProJira.key}",
                  "name": "${ProJira.name}",
                  "projectTypeKey": "software",
                  "simplified": false,
                },
                "summary": "Nouvelle plainte à PCP",
                "description": c.description,
                "issuetype": {"id": "${ProJira.idIssueType}"},
              },
            });
            log("body: " + body.toString());
            log("url: " + url.toString());
            var head = {
              'Content-Type': 'application/json',
              'Authorization':
                  'Basic ' +
                  base64Encode(
                    utf8.encode(
                      ConfigUrls.emailJira + ":" + ConfigUrls.password,
                    ),
                  ),
            };
            log("@@@@@@@@@@@@@@url@@@@@@@@@@@@@@ " + url.toString());
            var response = await http.post(url, body: body, headers: head);
            log("@@@@@@@@@@@@@@response body@@@@@@@@@@@@@@ " + response.body);
            // idJira =
            //     jsonDecode(response.body)["self"].toString() + "/attachments";
            idJira = jsonDecode(response.body)["self"].toString();
            await db.updateComplaintIdJira(c.id, idJira);
            log("@@@@@@@@@@@@@IDJira@@@@@@@" + idJira);
            log("@@@@@@@@@@@@@res@@@@@@@" + response.body.toString());
            log("@@@@@@@@@@@@@stat@@@@@@@" + response.statusCode.toString());
            c.idJira = idJira;
          } else {
            idJira = c.idJira;
          }

          // await db.updateComplaintIsSent(c.id, true);
          //     c.isSent = "true";

          // if (c.isSent == "true" || idJira != null) {
          //   var headers = {
          //     'Content-Type': 'multipart/form-data',
          //     'Authorization':
          //         'Basic ' +
          //         base64Encode(
          //           utf8.encode(
          //             ConfigUrls.emailJira + ":" + ConfigUrls.password,
          //           ),
          //         ),
          //     'X-Atlassian-Token': 'no-check',
          //   };

          //   var request = http.MultipartRequest(
          //     'POST',
          //     Uri.parse(idJira + "/attachments"),
          //   );
          //   request.headers.addAll(headers);
          //   // List<Attachment> attachments = await db
          //   //     .getAllAttachmentByComplaintId(c.id);
          //   // print("aaaaaaaaaaaaaaaaaaaaaaa " + attachments.length.toString());
          //   // for (var j = 0; j < attachments.length; j++) {
          //   //   String path = attachments[j].chemain;
          //   //   await addFile(request, File(path));
          //   // }
          //   print("@@@@@@@@@@@@@@@@@ " + c.id.toString());

          //   //  await request.send().then((onValue) async {
          //   //               if (onValue.statusCode == 200) {
          //   //                 await db.updateComplaintState(c.id, ComplaintState.Envoye);
          //   //                 //supprimer les attachments...
          //   //               } else {}
          //   //             });

          //   StreamedResponse response = await request.send();
          //   if (response.statusCode == 200) {
          //     await db.updateComplaintState(c.id, ComplaintState.Envoye);
          //     //supprimer les attachments...
          //   } else {}
          // }
          print(
            "********************************* next complaint ******************************************",
          );
          c = await db.getFirstComplaint();
        } else
          break;
      }
      isFetching = false;
    } else {
      isFetching = false;
      Timer(Duration(seconds: 15), () async {
        startFetchingDataTest2();
      });
    }
    print(
      "********************************* 2 Fetch II ******************************************",
    );
    // }
  }
}
