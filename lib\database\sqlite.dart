import 'package:marafik_app/database/complaint.dart';
import 'package:marafik_app/database/info_aswaq.dart';
import 'package:marafik_app/database/info_trans.dart';
import 'package:marafik_app/database/sector.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';
import 'package:sqflite/sqlite_api.dart';
import 'attachment.dart';
import 'info_batoire.dart';
import 'info_trash.dart';

enum ColorPrint { black, red, green, yellow, blue, magenta, cyan, white, non }

enum ComplaintState { Envoye, en_traitement, traite }

class DbHelper {
  static final _databaseName = "Complaint.db";
  static final colId = 'id';
  static final colQr = 'qr';

  static final tableSector = 'Sector';
  static final colNameFr = 'nameFr';
  static final colNameAr = 'nameAr';

  static final tableComplaint = 'Complaint';
  static final colLinkLocation = 'linkLocation';
  static final colDescription = 'description';
  static final colIdJira = 'idJira';
  static final colIsSent = 'isSent';
  static final colState = 'state';
  static final colIdSector = 'idSector';
  static final colDate = 'dateC';

  static final tableAttachment = 'Attachment';
  static final colChemain = 'chemain';
  static final colIdComplaint = 'idComplaint';

  static final tableInfoTrash = 'infoTrash';

  static final tableInfoBatoire = 'infoBatoire';

  static final tableInfoAswaq = 'infoAswaq';
  static final colQrShop = 'qrShop';
  static final colQrMatrix = 'qrMatrix';
  static final colQrOwner = 'qrOwner';

  static final tableInfoTrans = 'InfoTrans';
  static final colBusNum = 'busNum';
  static final colBusLine = 'busLine';

  static final DbHelper _instance = DbHelper.internal();
  factory DbHelper() => _instance;
  DbHelper.internal();

  //static Database _db=;

  Future<Database> createDatabase() async {
    // if(_db != null){
    //   return _db;
    //
    //define the path to the database
    String path = join(await getDatabasesPath(), _databaseName);
    Database db = await openDatabase(
      path,
      version: 1,
      onCreate: (Database db, int v) {
        //create all tables
        db.execute("""
                  CREATE TABLE $tableSector (
                        $colId INTEGER PRIMARY KEY,
                        $colNameFr TEXT,
                        $colNameAr TEXT
                        )
                """);

        db.execute("""
                  CREATE TABLE $tableComplaint (
                        $colId INTEGER PRIMARY KEY AUTOINCREMENT,
                        $colLinkLocation TEXT,
                        $colDescription TEXT,
                        $colIdJira TEXT,
                        $colIsSent TEXT,
                        $colState TEXT,
                        $colDate TEXT,
                        $colIdSector INTEGER,
                        FOREIGN KEY($colIdSector) REFERENCES $tableSector
                        )
                """);
        db.execute("""
                  CREATE TABLE $tableAttachment (
                        $colId INTEGER PRIMARY KEY AUTOINCREMENT,
                        $colChemain TEXT,
                        $colIdComplaint INTEGER,
                        FOREIGN KEY($colIdComplaint) REFERENCES $tableComplaint
                        )
                """);

        db.execute("""
                  CREATE TABLE $tableInfoTrash (
                        $colId INTEGER PRIMARY KEY AUTOINCREMENT,
                        $colQr TEXT
                        )
                """);

        db.execute("""
                  CREATE TABLE $tableInfoBatoire (
                        $colId INTEGER PRIMARY KEY AUTOINCREMENT,
                        $colQr TEXT
                        )
                """);

        db.execute("""
                  CREATE TABLE $tableInfoAswaq (
                        $colId INTEGER PRIMARY KEY AUTOINCREMENT,
                        $colQrShop TEXT,
                        $colQrMatrix TEXT,
                        $colQrOwner TEXT
                        )
                """);

        db.execute("""
                  CREATE TABLE $tableInfoTrans (
                        $colId INTEGER PRIMARY KEY AUTOINCREMENT,
                        $colBusNum TEXT,
                        $colBusLine TEXT
                        )
                """);
      },
    );
    return db;
  }

  //----------------- sector ---------------------------
  Future<int> insertSector(Sector sector) async {
    Database db = await createDatabase();
    return await db.insert(tableSector, sector.toMap());
  }

  Future<List<Sector>> getAllSector() async {
    Database db = await createDatabase();
    final List<Map<String, dynamic>> maps = await db.query(tableSector);
    return List.generate(maps.length, (i) {
      return Sector(maps[i][colId], maps[i][colNameFr], maps[i][colNameAr]);
    });
  }

  Future<Sector> getSectorById(int id) async {
    Database db = await createDatabase();
    final List<Map<String, dynamic>> maps = await db.query(
      tableSector,
      where: '$colId = ? ',
      whereArgs: [id],
    );
    return Sector(maps[0][colId], maps[0][colNameFr], maps[0][colNameAr]);
  }

  Future<int> deleteSectorById(int id) async {
    Database db = await createDatabase();
    return await db.delete(tableSector, where: '$colId = ? ', whereArgs: [id]);
  }

  Future<int> deleteAllSectors() async {
    Database db = await createDatabase();
    return await db.delete(tableSector);
  }

  //----------------- Complaint ---------------------------
  Future<int> insertComplaint(Complaint complaint) async {
    Database db = await createDatabase();
    return await db.insert(tableComplaint, complaint.toMap());
  }

  String convertToAr(String state) {
    switch (state) {
      case "Envoyé":
        return 'تم الإرسال';
      case "En attente":
        return 'في اللإنتظار';
      case "En traitement":
        return 'في المعالجة';
      case "Traité":
        return 'تم المعالجة';
    }
    return "";
  }

  Future<int> insertComplaintWithAttachment(
    Complaint complaint,
    List<Attachment> attachments,
  ) async {
    Database db = await createDatabase();
    //db.rawInsert('insert into courses')
    await db.insert(tableComplaint, complaint.toMap());
    int cr = await getCurrentComplaintId();
    for (var i = 0; i < attachments.length; i++) {
      attachments[i].idComplaint = cr;
      await insertAttachment(attachments[i]);
    }
    return 1;
  }

  Future<List<Complaint>> getAllComplaints(bool isAr) async {
    Database db = await createDatabase();
    final List<Map<String, dynamic>> maps = await db.query(tableComplaint);
    List<Complaint> complaints = [];
    for (var i = 0; i < maps.length; i++) {
      Sector s = await getSectorById(maps[i][colIdSector]);
      if (isAr) {
        Complaint c = Complaint(
          maps[i][colId],
          maps[i][colLinkLocation],
          maps[i][colDescription],
          maps[i][colIdJira],
          maps[i][colIsSent],
          maps[i][colState],
          maps[i][colDate],
          s.nameAr,
          maps[i][colIdSector],
        );
        c.state = convertToAr(maps[i][colState]);
        complaints.add(c);
      } else {
        complaints.add(
          Complaint(
            maps[i][colId],
            maps[i][colLinkLocation],
            maps[i][colDescription],
            maps[i][colIdJira],
            maps[i][colIsSent],
            maps[i][colState],
            maps[i][colDate],
            s.nameFr,
            maps[i][colIdSector],
          ),
        );
      }
    }
    return complaints;
  }

  Future<List<Complaint>> getAllComplaintsBySector(
    int idSector,
    bool isAr,
  ) async {
    Database db = await createDatabase();
    final List<Map<String, dynamic>> maps = await db.query(
      tableComplaint,
      where: '$colIdSector = ? ',
      whereArgs: [idSector],
    );
    List<Complaint> complaints = [];
    for (var i = 0; i < maps.length; i++) {
      Sector s = await getSectorById(maps[i][colIdSector]);
      if (isAr) {
        complaints.add(
          Complaint(
            maps[i][colId],
            maps[i][colLinkLocation],
            maps[i][colDescription],
            maps[i][colIdJira],
            maps[i][colIsSent],
            maps[i][colState],
            maps[i][colDate],
            s.nameAr,
            maps[i][colIdSector],
          ),
        );
      } else {
        complaints.add(
          Complaint(
            maps[i][colId],
            maps[i][colLinkLocation],
            maps[i][colDescription],
            maps[i][colIdJira],
            maps[i][colIsSent],
            maps[i][colState],
            maps[i][colDate],
            s.nameFr,
            maps[i][colIdSector],
          ),
        );
      }
    }
    return complaints;
  }

  Future<dynamic> getFirstComplaint() async {
    Database db = await createDatabase();
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      "select * from $tableComplaint where $colState = 'En attente' limit 1",
    );
    if (maps.length != 0)
      return Complaint(
        maps[0][colId],
        maps[0][colLinkLocation],
        maps[0][colDescription],
        maps[0][colIdJira],
        maps[0][colIsSent],
        maps[0][colState],
        maps[0][colDate],
        "",
        maps[0][colIdSector],
      );
    return null;
  }

  Future<List<Complaint>> getAllComplaintsNotProccessed() async {
    Database db = await createDatabase();
    final List<Map<String, dynamic>> maps = await db.query(
      tableComplaint,
      where: "$colState in('Envoyé','En traitement')",
    );
    return List.generate(maps.length, (i) {
      return Complaint(
        maps[i][colId],
        maps[i][colLinkLocation],
        maps[i][colDescription],
        maps[i][colIdJira],
        maps[i][colIsSent],
        maps[i][colState],
        maps[i][colDate],
        "",
        maps[i][colIdSector],
      );
    });
  }

  Future<int> getCurrentComplaintId() async {
    Database db = await createDatabase();
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      "SELECT * FROM SQLITE_SEQUENCE WHERE name='$tableComplaint'",
    );
    if (maps.length != 0) return maps[0]["seq"];
    return 0;
  }

  Future<void> updateComplaintIdJira(int id, String idJira) async {
    Database db = await createDatabase();
    await db.execute(
      "update $tableComplaint set $colIdJira = '$idJira' where id = $id ",
    );
  }

  Future<void> updateComplaintState(int id, ComplaintState state) async {
    Database db = await createDatabase();

    String s = "Traité";

    if (state == ComplaintState.Envoye)
      s = "Envoyé";
    else if (state == ComplaintState.en_traitement)
      s = "En traitement";

    await db.execute(
      "update $tableComplaint set $colState = '$s' where id = $id ",
    );
  }

  Future<void> updateComplaintIsSent(int id, bool isSent) async {
    Database db = await createDatabase();
    await db.execute(
      "update $tableComplaint set $colIsSent = '${isSent.toString()}' where id = $id ",
    );
  }

  Future<int> deleteComplaintById(int id) async {
    Database db = await createDatabase();
    return await db.delete(
      tableComplaint,
      where: '$colId = ? ',
      whereArgs: [id],
    );
  }

  Future<int> deleteAllComplaints() async {
    Database db = await createDatabase();
    return await db.delete(tableComplaint);
  }

  //----------------- Attachment ---------------------------
  Future<int> insertAttachment(Attachment attachment) async {
    Database db = await createDatabase();
    return await db.insert(tableAttachment, attachment.toMap());
  }

  Future<List<Attachment>> getAllAttachment() async {
    Database db = await createDatabase();
    final List<Map<String, dynamic>> maps = await db.query(tableAttachment);
    return List.generate(maps.length, (i) {
      return Attachment(
        maps[i][colId],
        maps[i][colChemain],
        maps[i][colIdComplaint],
      );
    });
  }

  Future<List<Attachment>> getAllAttachmentByComplaintId(
    int idComplaint,
  ) async {
    Database db = await createDatabase();
    final List<Map<String, dynamic>> maps = await db.query(
      tableAttachment,
      where: '$colIdComplaint = ?',
      whereArgs: [idComplaint],
    );
    return List.generate(maps.length, (i) {
      return Attachment(
        maps[i][colId],
        maps[i][colChemain],
        maps[i][colIdComplaint],
      );
    });
  }

  Future<int> deleteAttachmentById(int id) async {
    Database db = await createDatabase();
    return await db.delete(
      tableAttachment,
      where: '$colId = ? ',
      whereArgs: [id],
    );
  }

  Future<int> deleteAllAttachments() async {
    Database db = await createDatabase();
    return await db.delete(tableAttachment);
  }

  //----------------- InfoTrash ---------------------------
  Future<int> insertInfoTrash(InfoTrash infoTrash) async {
    Database db = await createDatabase();
    return await db.insert(tableInfoTrash, infoTrash.toMap());
  }

  Future<void> insertListOfInfoTrash(List<InfoTrash> list) async {
    for (var i = 0; i < list.length; i++) await insertInfoTrash(list[i]);
  }

  Future<List<InfoTrash>> getAllInfoTrash() async {
    Database db = await createDatabase();
    final List<Map<String, dynamic>> maps = await db.query(tableInfoTrash);
    return List.generate(maps.length, (i) {
      return InfoTrash(maps[i][colId], maps[i][colQr]);
    });
  }

  Future<dynamic> getTrashByQr(String qr) async {
    Database db = await createDatabase();
    final List<Map<String, dynamic>> maps = await db.query(
      tableInfoTrash,
      where: '$colQr = ? ',
      whereArgs: [qr],
    );
    if (maps.length == 0) {
      return null;
    }
    return maps[0][colQr];
  }

  Future<int> deleteInfoTrashById(int id) async {
    Database db = await createDatabase();
    return await db.delete(
      tableInfoTrash,
      where: '$colId = ? ',
      whereArgs: [id],
    );
  }

  Future<int> deleteAllInfoTrash() async {
    Database db = await createDatabase();
    return await db.delete(tableInfoTrash);
  }

  //----------------- InfoBatoire ---------------------------
  Future<int> insertInfoBatoire(InfoBatoire infoBatoire) async {
    Database db = await createDatabase();
    return await db.insert(tableInfoBatoire, infoBatoire.toMap());
  }

  Future<void> insertListOfInfoBatoire(List<InfoBatoire> list) async {
    for (var i = 0; i < list.length; i++) await insertInfoBatoire(list[i]);
  }

  Future<List<InfoBatoire>> getAllInfoBatoire() async {
    Database db = await createDatabase();
    final List<Map<String, dynamic>> maps = await db.query(tableInfoBatoire);
    return List.generate(maps.length, (i) {
      return InfoBatoire(maps[i][colId], maps[i][colQr]);
    });
  }

  Future<bool> isExistBatoire(String qr) async {
    Database db = await createDatabase();
    final List<Map<String, dynamic>> maps = await db.query(
      tableInfoBatoire,
      where: '$colQr = ?',
      whereArgs: [qr],
    );
    return maps.length != 0;
  }

  Future<int> deleteInfoBatoireById(int id) async {
    Database db = await createDatabase();
    return await db.delete(
      tableInfoBatoire,
      where: '$colId = ? ',
      whereArgs: [id],
    );
  }

  Future<int> deleteAllInfoBatoire() async {
    Database db = await createDatabase();
    return await db.delete(tableInfoBatoire);
  }

  //----------------- InfoAswaq ---------------------------
  Future<int> insertInfoAswaq(Shop infoAswaq) async {
    Database db = await createDatabase();
    return await db.insert(tableInfoAswaq, infoAswaq.toMap());
  }

  Future<void> insertListOfInfoAswaq(List<Shop> list) async {
    for (var i = 0; i < list.length; i++) await insertInfoAswaq(list[i]);
  }

  Future<List<Shop>> getAllInfoAswaq() async {
    Database db = await createDatabase();
    final List<Map<String, dynamic>> maps = await db.query(tableInfoAswaq);
    return List.generate(maps.length, (i) {
      return Shop(
        maps[i][colId],
        maps[i][colQrShop],
        maps[i][colQrMatrix],
        maps[i][colQrOwner],
      );
    });
  }

  Future<dynamic> getInfoAswaqByShopeQR(String qr) async {
    Database db = await createDatabase();
    final List<Map<String, dynamic>> maps = await db.query(
      tableInfoAswaq,
      where: '$colQrShop = ? or $colQrMatrix = ?',
      whereArgs: [qr, qr],
    );

    if (maps.length != 0)
      return Shop(
        maps[0][colId],
        maps[0][colQrShop],
        maps[0][colQrMatrix],
        maps[0][colQrOwner],
      );
    return null;
  }

  Future<String> getOwnerByShopeQR(String qr) async {
    // Shop aswaq = await getInfoAswaqByShopeQR(qr);
    // if (aswaq != null) return aswaq.colQrOwner;
    return "128192283";
  }

  Future<bool> isExistShop(String qr) async {
    Shop aswaq = await getInfoAswaqByShopeQR(qr);
    return true;
  }

  Future<int> deleteInfoAswaqById(int id) async {
    Database db = await createDatabase();
    return await db.delete(
      tableInfoAswaq,
      where: '$colId = ? ',
      whereArgs: [id],
    );
  }

  Future<int> deleteAllInfoAswaq() async {
    Database db = await createDatabase();
    return await db.delete(tableInfoAswaq);
  }

  //----------------- InfoTrans ---------------------------
  Future<int> insertInfoTrans(InfoTrans infoAswaq) async {
    Database db = await createDatabase();
    return await db.insert(tableInfoTrans, infoAswaq.toMap());
  }

  Future<void> insertListOfInfoTrans(List<InfoTrans> list) async {
    for (var i = 0; i < list.length; i++) await insertInfoTrans(list[i]);
  }

  Future<List<InfoTrans>> getAlltableInfoTrans() async {
    Database db = await createDatabase();
    final List<Map<String, dynamic>> maps = await db.query(tableInfoTrans);
    return List.generate(maps.length, (i) {
      return InfoTrans(maps[i][colId], maps[i][colBusNum], maps[i][colBusLine]);
    });
  }

  Future<dynamic> getTransByNumbre(String num) async {
    Database db = await createDatabase();
    final List<Map<String, dynamic>> maps = await db.query(
      tableInfoTrans,
      where: '$colBusNum = ? ',
      whereArgs: [num],
    );
    if (maps.length != 0)
      return InfoTrans(maps[0][colId], maps[0][colBusNum], maps[0][colBusLine]);

    return null;
  }

  Future<int> deleteInfoTransById(int id) async {
    Database db = await createDatabase();
    return await db.delete(
      tableInfoTrans,
      where: '$colId = ? ',
      whereArgs: [id],
    );
  }

  Future<int> deleteAllInfoTrans() async {
    Database db = await createDatabase();
    return await db.delete(tableInfoTrans);
  }

  //
  Future<void> executeQuery(String sql) async {
    Database db = await createDatabase();
    print(
      """"""
      """"""
      """araniiiiiiiiiiii dakhaaaaaaaaaaaaaaaaaaal"""
      """"""
      """""",
    );
    await db.execute(sql);
  }

  //
  Future<bool> isDatabaseFull() async {
    Database db = await createDatabase();
    final List<Map<String, dynamic>> maps = await db.rawQuery(
      "SELECT count(*) 'count' FROM InfoTrans",
    );
    return maps[0]["count"] != 0;
  }
}

void colorPrinter(String msg, {ColorPrint? color}) {
  switch (color.toString()) {
    case "ColorPrint.black":
      print('\x1B[30m$msg\x1B[0m');
      break;
    case "ColorPrint.red":
      print('\x1B[31m$msg\x1B[0m');
      break;
    case "ColorPrint.green":
      print('\x1B[32m$msg\x1B[0m');
      break;
    case "ColorPrint.yellow":
      print('\x1B[33m$msg\x1B[0m');
      break;
    case "ColorPrint.blue":
      print('\x1B[34m$msg\x1B[0m');
      break;
    case "ColorPrint.magenta":
      print('\x1B[35m$msg\x1B[0m');
      break;
    case "ColorPrint.cyan":
      print('\x1B[36m$msg\x1B[0m');
      break;
    case "ColorPrint.white":
      print('\x1B[37m$msg\x1B[0m');
      break;
    default:
      print(msg);
  }
}
