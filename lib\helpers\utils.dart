import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:map_launcher/map_launcher.dart';

class Utils {
  static void showMapOptions(
    double lat,
    double lng,
    BuildContext context,
  ) async {
    final availableMaps = await MapLauncher.installedMaps;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor:
          Colors.transparent, // Makes it transparent for custom styling
      barrierColor: Colors.black.withOpacity(0.5),
      isDismissible: true,
      enableDrag: true,
      builder: (BuildContext context) {
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                blurRadius: 20,
                offset: const Offset(0, -5),
              ),
            ],
          ),
          child: SafeArea(
            child: Padding(
              padding: EdgeInsets.only(
                bottom: MediaQuery.of(context).viewInsets.bottom,
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Drag Handle
                  Container(
                    width: 40.w,
                    height: 4.h,
                    margin: EdgeInsets.only(top: 12.r, bottom: 8.r),
                    decoration: BoxDecoration(
                      color: Colors.grey.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),

                  // Header Section
                  Container(
                    width: double.infinity,
                    padding: EdgeInsets.symmetric(
                      horizontal: 24.w,
                      vertical: 16.h,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        // Icon
                        Container(
                          padding: EdgeInsets.all(12.h),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            Icons.map_outlined,
                            color: Colors.black,
                            size: 28.h,
                          ),
                        ),
                        SizedBox(height: 12.h),

                        // Title
                        Text(
                          'Sélectionner une application de cartographie',
                          textAlign: TextAlign.center,
                          style: getBoldTextStyle(
                            color: Colors.black,
                            fontSize: 20.sp,
                          ),
                        ),
                        SizedBox(height: 4.h),

                        // Subtitle
                        Text(
                          'Sélectionner votre application de cartographie préférée',
                          style: getRegularTextStyle(
                            color: Colors.grey,
                            fontSize: 14.sp,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),

                  // Divider
                  Container(
                    height: 1,
                    margin: EdgeInsets.symmetric(horizontal: 24.w),
                    decoration: BoxDecoration(
                      color: Colors.grey.withOpacity(0.2),
                    ),
                  ),

                  // Map Options List
                  Flexible(
                    child: Container(
                      constraints: BoxConstraints(maxHeight: 300.h),
                      child: ListView.separated(
                        shrinkWrap: true,
                        padding: EdgeInsets.symmetric(
                          horizontal: 16.w,
                          vertical: 8.h,
                        ),
                        itemCount: availableMaps.length,
                        separatorBuilder:
                            (context, index) => SizedBox(height: 4.h),
                        itemBuilder: (context, index) {
                          final map = availableMaps[index];
                          return Container(
                            margin: EdgeInsets.symmetric(horizontal: 8.w),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: Colors.grey.withOpacity(0.1),
                                width: 1,
                              ),
                            ),
                            child: ListTile(
                              onTap: () {
                                Navigator.pop(context); // Close bottom sheet
                                map.showMarker(
                                  coords: Coords(lat, lng),
                                  title: "Location",
                                );
                              },
                              leading: Container(
                                padding: EdgeInsets.all(8.h),
                                decoration: BoxDecoration(
                                  color: Colors.white.withOpacity(0.05),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: SvgPicture.asset(
                                  map.icon,
                                  height: 24.h,
                                  width: 24.w,
                                ),
                              ),
                              title: Text(
                                map.mapName,
                                style: getMediumTextStyle(
                                  color: Colors.black,
                                  fontSize: 16.sp,
                                ),
                              ),
                              subtitle: Text(
                                'Ouvrir dans ${map.mapName}',
                                style: getRegularTextStyle(
                                  color: Colors.grey,
                                  fontSize: 12.sp,
                                ),
                              ),
                              trailing: Icon(
                                Icons.arrow_forward_ios,
                                color: Colors.grey,
                                size: 16.h,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
                  ),

                  // Bottom Padding
                  SizedBox(height: 16.h),

                  // Cancel Button (Optional)
                  Container(
                    width: double.infinity,
                    margin: EdgeInsets.symmetric(
                      horizontal: 24.w,
                      vertical: 8.h,
                    ),
                    child: TextButton(
                      onPressed: () => Navigator.pop(context),
                      style: TextButton.styleFrom(
                        padding: EdgeInsets.symmetric(vertical: 12.h),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                          side: BorderSide(color: Colors.grey.withOpacity(0.3)),
                        ),
                      ),
                      child: Text(
                        'Annuler',
                        style: getMediumTextStyle(
                          color: Colors.grey,
                          fontSize: 16.sp,
                        ),
                      ),
                    ),
                  ),

                  SizedBox(height: 8.h),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

TextStyle getMediumTextStyle({
  double fontSize = 12.0,
  required Color color,
  TextDecoration? decoration,
}) {
  return _getTextStyle(
    fontSize,
    FontWeight.w500,
    color,
    decoration: decoration,
  );
}

TextStyle _getTextStyle(
  double fontSize,
  FontWeight fontWeight,
  Color color, {
  TextDecoration? decoration,
}) {
  return TextStyle(
    fontSize: fontSize,
    fontWeight: fontWeight,
    color: color,
    decoration: decoration,
  );
}

TextStyle getRegularTextStyle({
  double fontSize = 12.0,
  required Color color,
  TextDecoration? decoration,
}) {
  return _getTextStyle(
    fontSize,
    FontWeight.w400,
    color,
    decoration: decoration,
  );
}

TextStyle getBoldTextStyle({
  double fontSize = 12.0,
  required Color color,
  TextDecoration? decoration,
}) {
  return _getTextStyle(
    fontSize,
    FontWeight.bold,
    color,
    decoration: decoration,
  );
}
