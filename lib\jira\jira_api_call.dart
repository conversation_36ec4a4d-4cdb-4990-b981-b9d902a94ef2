import 'dart:async';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:marafik_app/Models/data_manager.dart';
import 'package:marafik_app/Models/location.dart';
import 'package:marafik_app/database/attachment.dart';
import 'package:marafik_app/database/complaint.dart';
import 'package:marafik_app/database/fetch.dart';
import 'package:marafik_app/database/sector.dart';
import 'package:marafik_app/database/sqlite.dart';

class JiraApiCall {
  static addFile(http.MultipartRequest req, File file) async {
    Uint8List data = await file.readAsBytes();
    List<int> list = data.cast();
    req.files.addAll({
      http.MultipartFile.fromBytes(
        'file',
        list,
        filename: DateTime.now().toString(),
      ),
    });
  }

  static Future upload(BuildContext context, String description) async {
    String? desc;
    DbHelper db = DbHelper();
    var idCom = DateTime.now().millisecondsSinceEpoch;
    // if (DataManager.qrCode != "") {
    String qr;
    switch (DataManager.idSector) {
      case 1:
        qr = await db.getOwnerByShopeQR(DataManager.qrCode);
        print("qrrrrrrrrrrrrrrrr" + qr);
        desc = "Nouvelle plainte au marchand ${qr}  ";
        break;
      case 2:
        qr = DataManager.qrCode;
        desc =
            DataManager.qrCode != ""
                ? "Nouvelle plainte au  ${qr} "
                : "Nouvelle plainte au nettoiment de ";
        break;
      case 3:
        desc =
            DataManager.numBus != -1
                ? "Nouvelle plainte au bus N°${DataManager.numBus} "
                : "Nouvelle plainte au station de";
        break;
      case 4:
        desc = "Nouvelle plainte au Espace vert de ";
        break;
      case 5:
        qr = DataManager.qrCode;
        desc = "Nouvelle plainte au ${qr}  ";
        break;
      default:
    }
    // }
    print("---------------- ${DataManager.idSector}------------------------");
    Complaint complaint = Complaint.creat(
      LocationHandel.constractUrl(),
      desc! + description,
      DataManager.idSector,
    );
    // Complaint.creat(idCom, DataManager.qrCode, LocationHandel.locationData.latitude, LocationHandel.locationData.longitude, description);

    List<Attachment> atts = [];
    DataManager.getAttachments().forEach((element) {
      atts.add(Attachment.creat(element.path, idCom));
    });
    print("ataaaaaaaaaaachhhhhhhhhhhhh" + atts.length.toString());
    await db.insertComplaintWithAttachment(complaint, atts);

    FetchData.startFetchingDataTest2();
  }
}
