name: marafik_app
description: "A new Flutter project."

publish_to: "none"

version: 1.0.0+1

environment:
  sdk: ^3.7.0

dependencies:
  flutter:
    sdk: flutter

  cupertino_icons: ^1.0.8
  flutter_screenutil:
  intl:
  http:
  hexcolor:
  flutter_switch:
  fluttertoast:
  flutter_svg:
  liquid_swipe:
  video_player:
  path_provider:
  image_picker:
  location:
  sqflite:
  app_settings:
  shared_preferences:
  google_maps_flutter: ^2.1.0
  permission_handler: ^12.0.1
  qr_code_scanner: ^1.0.1
  flutter_sound: ^9.2.13
  flutter_dotenv: ^5.2.1
  map_launcher: ^3.5.0
dev_dependencies:
  flutter_test:
    sdk: flutter

  flutter_lints: ^5.0.0

flutter:
  uses-material-design: true

  assets:
    - assets/
    - assets/berkane.png
    - assets/pcp-icon.png
    - assets/wms-icon.png
    - assets/bus-icon.png
    - assets/espace-icon.png
    - assets/bat-icon.png
    - assets/backsimpledesc.png
    - assets/checkIcon.png
    - assets/empty.png
    - assets/footer.png
    - assets/icon_market.png
    - assets/icon.png
    - assets/logo_marafik.png
    - assets/logo.svg
    - assets/menu_pic.png
    - assets/menu_pic.svg
    - assets/micLogo.png
    - assets/qrCode.png
    - assets/read_blog.png
    - assets/splash.m4v
    - assets/logo_agadir.png
    - assets/water_pipe.png
    - assets/road.png
    - assets/security.png
    - assets/agadir.jpeg
    - assets/plage.jpg
    - assets/Souk El Had.jpeg
    - assets/marina-agadir.jpg
    - assets/oufella.jpg
    - assets/vallee-des-oiseaux.jpg
    - assets/crocoparc.jpg
    - assets/Golf-du-Soleil-Agadir.jpg
    - assets/musee.jpg
    - assets/.env

  fonts:
    - family: Roboto Bold
      fonts:
        - asset: fonts/Roboto-Bold.ttf
      # fonts:
      #   - asset: fonts/Roboto-Regular.ttf
      #   - asset: fonts/Roboto-Medium.ttf
      #         style: italic
    - family: Roboto Regular
      fonts:
        - asset: fonts/Roboto-Regular.ttf
    - family: Roboto Medium
      fonts:
        - asset: fonts/Roboto-Medium.ttf

flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icon.png"
